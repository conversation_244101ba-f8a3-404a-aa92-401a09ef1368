using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MedicalCenterWinForms.Models;

namespace MedicalCenterWinForms.Services
{
    public class ReportExportService
    {
        public async Task<bool> ExportDailyReportToCSV(DailyFinancialSummary summary, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Header
                csv.AppendLine("تقرير مالي يومي");
                csv.AppendLine($"التاريخ: {summary.Date:yyyy-MM-dd}");
                csv.AppendLine("");
                
                // Summary
                csv.AppendLine("الملخص");
                csv.AppendLine("البيان,القيمة");
                csv.AppendLine($"عدد المدفوعات الرئيسية,{summary.MainPaymentsCount}");
                csv.AppendLine($"عدد مدفوعات التحويل,{summary.ReferralPaymentsCount}");
                csv.AppendLine($"إجمالي المدفوعات الرئيسية,{summary.TotalMainPayments:C}");
                csv.AppendLine($"إجمالي مدفوعات التحويل,{summary.TotalReferralPayments:C}");
                csv.AppendLine($"إجمالي الإيرادات,{summary.TotalRevenue:C}");
                csv.AppendLine($"إجمالي حصص الأطباء,{summary.TotalDoctorShares:C}");
                csv.AppendLine($"إجمالي حصص المركز,{summary.TotalCenterShares:C}");
                csv.AppendLine("");
                
                // Main Payments Details
                if (summary.MainPaymentDetails.Any())
                {
                    csv.AppendLine("تفاصيل المدفوعات الرئيسية");
                    csv.AppendLine("رقم المدفوعة,اسم المراجع,اسم الطبيب,رسوم الاستشارة,رسوم الفحص,حصة الطبيب,حصة المركز,وقت الدفع");
                    
                    foreach (var payment in summary.MainPaymentDetails)
                    {
                        csv.AppendLine($"MP{payment.MainPaymentId:D4}," +
                                     $"{payment.PatientVisit.Patient.PatientName}," +
                                     $"{payment.PatientVisit.Doctor.FullName}," +
                                     $"{payment.ConsultationFee}," +
                                     $"{payment.ExamFee}," +
                                     $"{payment.DoctorShare}," +
                                     $"{payment.CenterShare}," +
                                     $"{payment.PaymentDate:HH:mm}");
                    }
                    csv.AppendLine("");
                }
                
                // Referral Payments Details
                if (summary.ReferralPaymentDetails.Any())
                {
                    csv.AppendLine("تفاصيل مدفوعات التحويل");
                    csv.AppendLine("رقم المدفوعة,اسم المراجع,اسم الطبيب,اسم الخدمة,المبلغ,حصة الطبيب,حصة المركز,وقت الدفع");
                    
                    foreach (var payment in summary.ReferralPaymentDetails)
                    {
                        csv.AppendLine($"RP{payment.ReferralPaymentId:D4}," +
                                     $"{payment.PatientVisit.Patient.PatientName}," +
                                     $"{payment.PatientVisit.Doctor.FullName}," +
                                     $"{payment.MedicalService.ServiceName}," +
                                     $"{payment.Amount}," +
                                     $"{payment.DoctorShare ?? 0}," +
                                     $"{payment.CenterShare}," +
                                     $"{payment.PaymentDate:HH:mm}");
                    }
                }
                
                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ExportMonthlyReportToCSV(MonthlyFinancialSummary summary, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Header
                csv.AppendLine("تقرير مالي شهري");
                csv.AppendLine($"الشهر: {summary.MonthName}");
                csv.AppendLine("");
                
                // Summary
                csv.AppendLine("الملخص الشهري");
                csv.AppendLine("البيان,القيمة");
                csv.AppendLine($"إجمالي الإيرادات,{summary.TotalRevenue:C}");
                csv.AppendLine($"إجمالي المدفوعات الرئيسية,{summary.TotalMainPayments:C}");
                csv.AppendLine($"إجمالي مدفوعات التحويل,{summary.TotalReferralPayments:C}");
                csv.AppendLine($"إجمالي حصص الأطباء,{summary.TotalDoctorShares:C}");
                csv.AppendLine($"إجمالي حصص المركز,{summary.TotalCenterShares:C}");
                csv.AppendLine($"عدد المدفوعات,{summary.TotalPaymentsCount}");
                csv.AppendLine($"أيام العمل,{summary.WorkingDays}");
                csv.AppendLine($"متوسط الإيرادات اليومية,{summary.AverageDailyRevenue:C}");
                csv.AppendLine("");
                
                // Daily breakdown
                csv.AppendLine("التفصيل اليومي");
                csv.AppendLine("التاريخ,المدفوعات الرئيسية,مدفوعات التحويل,إجمالي الإيرادات,حصص الأطباء,حصص المركز,عدد المدفوعات");
                
                foreach (var daily in summary.DailySummaries.Where(d => d.TotalPayments > 0))
                {
                    csv.AppendLine($"{daily.Date:yyyy-MM-dd}," +
                                 $"{daily.TotalMainPayments}," +
                                 $"{daily.TotalReferralPayments}," +
                                 $"{daily.TotalRevenue}," +
                                 $"{daily.TotalDoctorShares}," +
                                 $"{daily.TotalCenterShares}," +
                                 $"{daily.TotalPayments}");
                }
                
                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ExportDoctorPerformanceToCSV(List<DoctorPerformanceReport> reports, 
                                                            DateTime startDate, DateTime endDate, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Header
                csv.AppendLine("تقرير أداء الأطباء");
                csv.AppendLine($"الفترة: من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
                csv.AppendLine("");
                
                // Summary
                csv.AppendLine("ملخص الأداء");
                csv.AppendLine("اسم الطبيب,التخصص,عدد المراجعين,إجمالي الإيرادات,حصص الطبيب,متوسط المراجع,المدفوعات الرئيسية,مدفوعات التحويل");
                
                foreach (var report in reports)
                {
                    csv.AppendLine($"{report.Doctor.FullName}," +
                                 $"{report.Doctor.Specialty}," +
                                 $"{report.TotalPatients}," +
                                 $"{report.TotalRevenue}," +
                                 $"{report.TotalDoctorShares}," +
                                 $"{report.AverageRevenuePerPatient}," +
                                 $"{report.TotalMainPayments}," +
                                 $"{report.TotalReferralPayments}");
                }
                
                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ExportServiceAnalysisToCSV(List<ServicePerformanceReport> reports, 
                                                          DateTime startDate, DateTime endDate, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Header
                csv.AppendLine("تقرير تحليل الخدمات");
                csv.AppendLine($"الفترة: من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
                csv.AppendLine("");
                
                // Summary
                csv.AppendLine("تحليل الخدمات");
                csv.AppendLine("اسم الخدمة,نوع الخدمة,عدد الاستخدام,إجمالي الإيرادات,متوسط السعر,حصص الأطباء,حصص المركز");
                
                var totalRevenue = reports.Sum(r => r.TotalRevenue);
                
                foreach (var report in reports)
                {
                    var revenuePercentage = totalRevenue > 0 ? (report.TotalRevenue / totalRevenue) * 100 : 0;
                    
                    csv.AppendLine($"{report.Service.ServiceName}," +
                                 $"{(report.Service.ServiceType == "Direct" ? "مباشر" : "تحويل")}," +
                                 $"{report.TotalPayments}," +
                                 $"{report.TotalRevenue}," +
                                 $"{report.AverageRevenuePerPayment}," +
                                 $"{report.TotalDoctorShares}," +
                                 $"{report.TotalCenterShares}");
                }
                
                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ExportFinancialTrendsToCSV(FinancialTrendsReport report, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Header
                csv.AppendLine("تقرير الاتجاهات المالية");
                csv.AppendLine($"الفترة: من {report.StartDate:yyyy-MM-dd} إلى {report.EndDate:yyyy-MM-dd}");
                csv.AppendLine("");
                
                // Summary
                csv.AppendLine("الملخص العام");
                csv.AppendLine("البيان,القيمة");
                csv.AppendLine($"إجمالي الإيرادات,{report.TotalRevenue:C}");
                csv.AppendLine($"إجمالي حصص الأطباء,{report.TotalDoctorShares:C}");
                csv.AppendLine($"إجمالي حصص المركز,{report.TotalCenterShares:C}");
                csv.AppendLine($"متوسط الإيرادات الشهرية,{report.AverageMonthlyRevenue:C}");
                csv.AppendLine($"معدل نمو الإيرادات,{report.RevenueGrowthRate:F2}%");
                csv.AppendLine($"معدل نمو المدفوعات,{report.PaymentGrowthRate:F2}%");
                csv.AppendLine("");
                
                // Monthly breakdown
                csv.AppendLine("التفصيل الشهري");
                csv.AppendLine("الشهر,إجمالي الإيرادات,المدفوعات الرئيسية,مدفوعات التحويل,حصص الأطباء,حصص المركز,عدد المدفوعات,أيام العمل");
                
                foreach (var monthly in report.MonthlyData)
                {
                    csv.AppendLine($"{monthly.MonthName}," +
                                 $"{monthly.TotalRevenue}," +
                                 $"{monthly.TotalMainPayments}," +
                                 $"{monthly.TotalReferralPayments}," +
                                 $"{monthly.TotalDoctorShares}," +
                                 $"{monthly.TotalCenterShares}," +
                                 $"{monthly.TotalPaymentsCount}," +
                                 $"{monthly.WorkingDays}");
                }
                
                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public string ShowSaveFileDialog(string defaultFileName, string filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*")
        {
            using var saveDialog = new SaveFileDialog
            {
                FileName = defaultFileName,
                Filter = filter,
                DefaultExt = "csv",
                AddExtension = true
            };

            return saveDialog.ShowDialog() == DialogResult.OK ? saveDialog.FileName : string.Empty;
        }
    }
}
