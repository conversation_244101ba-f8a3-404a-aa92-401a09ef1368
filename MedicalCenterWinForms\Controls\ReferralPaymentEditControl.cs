using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class ReferralPaymentEditControl : BaseUserControl
    {
        private ReferralPayment? _referralPayment;
        private bool _isEditMode;
        private PaymentCalculationService _paymentCalculationService;

        // UI Components
        private Panel mainPanel;
        private Panel buttonPanel;
        private ComboBox cmbPatient;
        private ComboBox cmbDoctor;
        private ComboBox cmbMedicalService;
        private DateTimePicker dtpPaymentDate;
        private NumericUpDown nudAmount;
        private NumericUpDown nudDoctorShare;
        private NumericUpDown nudCenterShare;
        private TextBox txtNotes;
        private Button btnSave;
        private Button btnCancel;
        private Button btnCalculateShares;
        private CheckBox chkAutoCalculate;

        // Labels
        private Label lblPatient;
        private Label lblDoctor;
        private Label lblMedicalService;
        private Label lblPaymentDate;
        private Label lblAmount;
        private Label lblDoctorShare;
        private Label lblCenterShare;
        private Label lblNotes;
        private Label lblCalculationInfo;
        private Label lblServiceInfo;

        public event EventHandler? ReferralPaymentSaved;

        public ReferralPaymentEditControl() : base()
        {
            InitializeComponent();
            InitializePaymentService();
            _isEditMode = false;
        }

        public ReferralPaymentEditControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            _isEditMode = false;
            LoadComboBoxData();
        }

        public ReferralPaymentEditControl(DatabaseService databaseService, ReferralPayment referralPayment) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            _referralPayment = referralPayment;
            _isEditMode = true;
            LoadComboBoxData();
            LoadReferralPaymentData();
        }

        private void InitializePaymentService()
        {
            _paymentCalculationService = new PaymentCalculationService(DatabaseService);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(800, 750);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateButtonPanel();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                _isEditMode ? "تعديل مدفوعة تحويل" : "إضافة مدفوعة تحويل", true);
            mainPanel.Size = new Size(780, 670);
            mainPanel.Location = new Point(10, 10);

            int yPos = 50;
            int labelWidth = 120;
            int controlWidth = 200;
            int spacing = 40;

            // Patient selection
            lblPatient = CreateLabel("المراجع:", new Point(620, yPos));
            cmbPatient = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbPatient.Location = new Point(400, yPos);
            cmbPatient.Size = new Size(controlWidth, 30);

            yPos += spacing;

            // Doctor selection
            lblDoctor = CreateLabel("الطبيب:", new Point(620, yPos));
            cmbDoctor = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbDoctor.Location = new Point(400, yPos);
            cmbDoctor.Size = new Size(controlWidth, 30);
            cmbDoctor.SelectedIndexChanged += CmbDoctor_SelectedIndexChanged;

            yPos += spacing;

            // Medical Service selection
            lblMedicalService = CreateLabel("الخدمة الطبية:", new Point(620, yPos));
            cmbMedicalService = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbMedicalService.Location = new Point(400, yPos);
            cmbMedicalService.Size = new Size(controlWidth, 30);
            cmbMedicalService.SelectedIndexChanged += CmbMedicalService_SelectedIndexChanged;

            yPos += spacing;

            // Service Info Label
            lblServiceInfo = new Label
            {
                Text = "اختر الطبيب والخدمة لعرض معلومات الحساب",
                Location = new Point(200, yPos),
                Size = new Size(400, 20),
                Font = ArabicFontHelper.GetArabicFont(8F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.TopCenter
            };

            yPos += 30;

            // Payment Date
            lblPaymentDate = CreateLabel("تاريخ الدفع:", new Point(620, yPos));
            dtpPaymentDate = new DateTimePicker
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 30),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            yPos += spacing;

            // Amount
            lblAmount = CreateLabel("المبلغ:", new Point(620, yPos));
            nudAmount = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2
            };
            nudAmount.ValueChanged += NudAmount_ValueChanged;

            yPos += spacing;

            // Auto Calculate Checkbox
            chkAutoCalculate = new CheckBox
            {
                Text = "حساب تلقائي للحصص",
                Location = new Point(500, yPos),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                Checked = true
            };
            chkAutoCalculate.CheckedChanged += ChkAutoCalculate_CheckedChanged;

            // Calculate Button
            btnCalculateShares = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🧮 احسب الحصص", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnCalculateShares.Location = new Point(350, yPos);
            btnCalculateShares.Size = new Size(120, 30);
            btnCalculateShares.Click += BtnCalculateShares_Click;

            yPos += spacing;

            // Doctor Share
            lblDoctorShare = CreateLabel("حصة الطبيب:", new Point(620, yPos));
            nudDoctorShare = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2,
                ReadOnly = true,
                BackColor = Color.LightGray
            };

            yPos += spacing;

            // Center Share
            lblCenterShare = CreateLabel("حصة المركز:", new Point(620, yPos));
            nudCenterShare = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2,
                ReadOnly = true,
                BackColor = Color.LightGray
            };

            yPos += spacing;

            // Calculation Info
            lblCalculationInfo = new Label
            {
                Text = "سيتم حساب الحصص تلقائياً بناءً على إعدادات الطبيب والخدمة",
                Location = new Point(200, yPos),
                Size = new Size(400, 40),
                Font = ArabicFontHelper.GetArabicFont(8F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.TopCenter
            };

            yPos += 60;

            // Notes
            lblNotes = CreateLabel("ملاحظات:", new Point(620, yPos));
            txtNotes = ModernMedicalTheme.Components.CreateAdvancedTextBox("ملاحظات إضافية...");
            txtNotes.Location = new Point(200, yPos);
            txtNotes.Size = new Size(400, 60);
            txtNotes.Multiline = true;

            mainPanel.Controls.AddRange(new Control[]
            {
                lblPatient, cmbPatient, lblDoctor, cmbDoctor, lblMedicalService, cmbMedicalService,
                lblServiceInfo, lblPaymentDate, dtpPaymentDate, lblAmount, nudAmount,
                chkAutoCalculate, btnCalculateShares, lblDoctorShare, nudDoctorShare,
                lblCenterShare, nudCenterShare, lblCalculationInfo, lblNotes, txtNotes
            });

            this.Controls.Add(mainPanel);
        }

        private void CreateButtonPanel()
        {
            buttonPanel = new Panel
            {
                Location = new Point(10, 690),
                Size = new Size(780, 50),
                BackColor = Color.Transparent
            };

            btnSave = ModernMedicalTheme.Components.CreateAdvancedButton(
                "💾 حفظ", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnSave.Location = new Point(600, 10);
            btnSave.Size = new Size(80, 35);
            btnSave.Click += BtnSave_Click;

            btnCancel = ModernMedicalTheme.Components.CreateAdvancedButton(
                "❌ إلغاء", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnCancel.Location = new Point(510, 10);
            btnCancel.Size = new Size(80, 35);
            btnCancel.Click += BtnCancel_Click;

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });
            this.Controls.Add(buttonPanel);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(9F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                TextAlign = ContentAlignment.MiddleRight
            };
        }

        private async void LoadComboBoxData()
        {
            try
            {
                using var context = DatabaseService.GetDbContext();

                // Load patient visits
                var patientVisits = await context.PatientVisits
                    .Include(pv => pv.Patient)
                    .Include(pv => pv.Doctor)
                    .OrderByDescending(pv => pv.VisitDate)
                    .Take(100) // Load recent 100 patient visits
                    .ToListAsync();

                cmbPatient.Items.Clear();
                foreach (var visit in patientVisits)
                {
                    cmbPatient.Items.Add(new {
                        Text = $"{visit.Patient.PatientName} - {visit.VisitDate:yyyy-MM-dd}",
                        Value = visit.PatientVisitId
                    });
                }
                cmbPatient.DisplayMember = "Text";
                cmbPatient.ValueMember = "Value";

                // Load doctors
                var doctors = await context.Doctors
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();

                cmbDoctor.Items.Clear();
                foreach (var doctor in doctors)
                {
                    cmbDoctor.Items.Add(new { Text = doctor.FullName, Value = doctor.DoctorId });
                }
                cmbDoctor.DisplayMember = "Text";
                cmbDoctor.ValueMember = "Value";

                // Load medical services (referral type)
                var services = await context.MedicalServices
                    .Where(s => s.ServiceType == "Referral")
                    .OrderBy(s => s.ServiceName)
                    .ToListAsync();

                cmbMedicalService.Items.Clear();
                foreach (var service in services)
                {
                    cmbMedicalService.Items.Add(new { Text = service.ServiceName, Value = service.MedicalServiceId });
                }
                cmbMedicalService.DisplayMember = "Text";
                cmbMedicalService.ValueMember = "Value";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private void LoadReferralPaymentData()
        {
            if (_referralPayment == null) return;

            try
            {
                // Set patient visit
                for (int i = 0; i < cmbPatient.Items.Count; i++)
                {
                    dynamic item = cmbPatient.Items[i];
                    if (item.Value == _referralPayment.PatientVisitId)
                    {
                        cmbPatient.SelectedIndex = i;
                        break;
                    }
                }

                // Set doctor (from patient visit)
                if (_referralPayment.PatientVisit?.DoctorId != null)
                {
                    for (int i = 0; i < cmbDoctor.Items.Count; i++)
                    {
                        dynamic item = cmbDoctor.Items[i];
                        if (item.Value == _referralPayment.PatientVisit.DoctorId)
                        {
                            cmbDoctor.SelectedIndex = i;
                            break;
                        }
                    }
                }

                // Set medical service
                for (int i = 0; i < cmbMedicalService.Items.Count; i++)
                {
                    dynamic item = cmbMedicalService.Items[i];
                    if (item.Value == _referralPayment.MedicalServiceId)
                    {
                        cmbMedicalService.SelectedIndex = i;
                        break;
                    }
                }

                // Set other properties
                dtpPaymentDate.Value = _referralPayment.PaymentDate;
                nudAmount.Value = _referralPayment.Amount;
                nudDoctorShare.Value = _referralPayment.DoctorShare ?? 0;
                nudCenterShare.Value = _referralPayment.CenterShare;
                txtNotes.Text = _referralPayment.Notes ?? "";

                UpdateServiceInfo();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات المدفوعة: {ex.Message}");
            }
        }

        // Event Handlers
        private async void CmbDoctor_SelectedIndexChanged(object sender, EventArgs e)
        {
            await UpdateServiceInfo();
            if (chkAutoCalculate.Checked)
            {
                await CalculateShares();
            }
        }

        private async void CmbMedicalService_SelectedIndexChanged(object sender, EventArgs e)
        {
            await UpdateServiceInfo();
            await LoadDefaultPrice();
            if (chkAutoCalculate.Checked)
            {
                await CalculateShares();
            }
        }

        private async void NudAmount_ValueChanged(object sender, EventArgs e)
        {
            if (chkAutoCalculate.Checked)
            {
                await CalculateShares();
            }
        }

        private void ChkAutoCalculate_CheckedChanged(object sender, EventArgs e)
        {
            nudDoctorShare.ReadOnly = chkAutoCalculate.Checked;
            nudCenterShare.ReadOnly = chkAutoCalculate.Checked;

            if (chkAutoCalculate.Checked)
            {
                nudDoctorShare.BackColor = Color.LightGray;
                nudCenterShare.BackColor = Color.LightGray;
                lblCalculationInfo.Text = "سيتم حساب الحصص تلقائياً بناءً على إعدادات الطبيب والخدمة";
            }
            else
            {
                nudDoctorShare.BackColor = Color.White;
                nudCenterShare.BackColor = Color.White;
                lblCalculationInfo.Text = "يمكنك تعديل الحصص يدوياً";
            }
        }

        private async void BtnCalculateShares_Click(object sender, EventArgs e)
        {
            await CalculateShares();
        }

        private async Task UpdateServiceInfo()
        {
            try
            {
                if (cmbDoctor.SelectedValue == null || cmbMedicalService.SelectedValue == null)
                {
                    lblServiceInfo.Text = "اختر الطبيب والخدمة لعرض معلومات الحساب";
                    lblServiceInfo.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
                    return;
                }

                var doctorId = (int)cmbDoctor.SelectedValue;
                var serviceId = (int)cmbMedicalService.SelectedValue;

                var (isValid, errorMessage) = await _paymentCalculationService
                    .ValidateDoctorServiceConfiguration(doctorId, serviceId);

                if (isValid)
                {
                    var doctorService = await _paymentCalculationService
                        .GetDoctorServiceDetails(doctorId, serviceId);

                    if (doctorService != null)
                    {
                        var infoText = "";
                        if (doctorService.IsFixedAmount && doctorService.FixedAmount.HasValue)
                        {
                            infoText = $"مبلغ مقطوع: {doctorService.FixedAmount.Value:C}";
                        }
                        else if (doctorService.HasPercentage && doctorService.Percentage.HasValue)
                        {
                            infoText = $"نسبة: {doctorService.Percentage.Value:F2}%";
                            if (doctorService.ServiceCost.HasValue)
                            {
                                infoText += $" (بعد خصم تكلفة: {doctorService.ServiceCost.Value:C})";
                            }
                        }

                        lblServiceInfo.Text = infoText;
                        lblServiceInfo.ForeColor = MaterialDesignHelper.Colors.Success;
                    }
                }
                else
                {
                    lblServiceInfo.Text = errorMessage;
                    lblServiceInfo.ForeColor = MaterialDesignHelper.Colors.Error;
                }
            }
            catch (Exception ex)
            {
                lblServiceInfo.Text = $"خطأ في تحميل معلومات الخدمة: {ex.Message}";
                lblServiceInfo.ForeColor = MaterialDesignHelper.Colors.Error;
            }
        }

        private async Task LoadDefaultPrice()
        {
            try
            {
                if (cmbDoctor.SelectedValue == null || cmbMedicalService.SelectedValue == null)
                    return;

                var doctorId = (int)cmbDoctor.SelectedValue;
                var serviceId = (int)cmbMedicalService.SelectedValue;

                var defaultPrice = await _paymentCalculationService
                    .GetDoctorServiceDefaultPrice(doctorId, serviceId);

                if (defaultPrice.HasValue && nudAmount.Value == 0)
                {
                    nudAmount.Value = defaultPrice.Value;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل السعر الافتراضي: {ex.Message}");
            }
        }

        private async Task CalculateShares()
        {
            try
            {
                if (cmbDoctor.SelectedValue == null || cmbMedicalService.SelectedValue == null)
                {
                    lblCalculationInfo.Text = "يرجى اختيار الطبيب والخدمة أولاً";
                    lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Error;
                    return;
                }

                var doctorId = (int)cmbDoctor.SelectedValue;
                var serviceId = (int)cmbMedicalService.SelectedValue;
                var amount = nudAmount.Value;

                if (amount <= 0)
                {
                    lblCalculationInfo.Text = "يرجى إدخال مبلغ صحيح";
                    lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Error;
                    return;
                }

                var (doctorShare, centerShare) = await _paymentCalculationService
                    .CalculateReferralPaymentShares(doctorId, serviceId, amount);

                nudDoctorShare.Value = doctorShare;
                nudCenterShare.Value = centerShare;

                lblCalculationInfo.Text = $"تم حساب الحصص بناءً على إعدادات الطبيب والخدمة";
                lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Success;
            }
            catch (Exception ex)
            {
                lblCalculationInfo.Text = $"خطأ في حساب الحصص: {ex.Message}";
                lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Error;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
            {
                return;
            }

            try
            {
                SetLoadingState(true);
                btnSave.Text = "جاري الحفظ...";

                using var context = DatabaseService.GetDbContext();

                if (_isEditMode && _referralPayment != null)
                {
                    var existingPayment = await context.ReferralPayments.FindAsync(_referralPayment.ReferralPaymentId);
                    if (existingPayment != null)
                    {
                        UpdateReferralPaymentFromForm(existingPayment);
                    }
                }
                else
                {
                    var newPayment = new ReferralPayment();
                    UpdateReferralPaymentFromForm(newPayment);
                    context.ReferralPayments.Add(newPayment);
                }

                await context.SaveChangesAsync();

                ShowSuccess(_isEditMode ? "تم تحديث المدفوعة بنجاح" : "تم إضافة المدفوعة بنجاح");
                ReferralPaymentSaved?.Invoke(this, EventArgs.Empty);

                // Close the dialog
                this.ParentForm?.Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
                btnSave.Text = "💾 حفظ";
            }
        }

        private void UpdateReferralPaymentFromForm(ReferralPayment payment)
        {
            if (!_isEditMode)
            {
                payment.PaymentDate = dtpPaymentDate.Value;
            }

            payment.PatientVisitId = (int)cmbPatient.SelectedValue;
            payment.MedicalServiceId = (int)cmbMedicalService.SelectedValue;
            payment.Amount = nudAmount.Value;
            payment.DoctorShare = nudDoctorShare.Value;
            payment.CenterShare = nudCenterShare.Value;
            payment.Notes = txtNotes.Text.Trim();
        }

        private bool ValidateInput()
        {
            if (cmbPatient.SelectedValue == null)
            {
                ShowError("يرجى اختيار زيارة المراجع");
                cmbPatient.Focus();
                return false;
            }

            if (cmbMedicalService.SelectedValue == null)
            {
                ShowError("يرجى اختيار الخدمة الطبية");
                cmbMedicalService.Focus();
                return false;
            }

            if (nudAmount.Value <= 0)
            {
                ShowError("يجب أن يكون المبلغ أكبر من صفر");
                nudAmount.Focus();
                return false;
            }

            var totalShares = nudDoctorShare.Value + nudCenterShare.Value;
            if (Math.Abs(nudAmount.Value - totalShares) > 0.01m)
            {
                ShowError($"مجموع الحصص ({totalShares:C}) لا يساوي المبلغ ({nudAmount.Value:C})");
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.ParentForm?.Close();
        }
    }
}
