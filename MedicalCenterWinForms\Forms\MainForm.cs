using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Controls;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Forms
{
    public partial class MainForm : Form
    {
        private readonly DatabaseService _databaseService;
        private UserControl? _currentControl;
        private Panel? _activeNavButton;
        private string _currentUser = "المدير العام";
        private string _userRole = "admin";

        public MainForm(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            InitializeModernUI();
            LoadDashboard();
        }

        private void InitializeModernUI()
        {
            // Set modern form properties
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);

            // Configure for RTL
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set modern title
            this.Text = "نظام إدارة المركز الطبي - 2025";

            // Initialize with dashboard
            SetActiveNavButton(navDashboard);
        }

        private void LoadDashboard()
        {
            var dashboardControl = new DashboardControl(_databaseService);
            ShowUserControl(dashboardControl);
        }

        private void ShowUserControl(UserControl userControl)
        {
            // Clear current control
            if (_currentControl != null)
            {
                contentPanel.Controls.Remove(_currentControl);
                _currentControl.Dispose();
            }

            // Add new control
            _currentControl = userControl;
            userControl.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(userControl);
            userControl.BringToFront();
        }

        private void SetActiveNavButton(Panel activeButton)
        {
            // Reset all navigation buttons
            var navButtons = new Panel[]
            {
                navDashboard, navPatients, navDoctors, navServices,
                navPayments, navReports, navSettings
            };

            foreach (var button in navButtons)
            {
                if (button?.Tag is Action<bool> setActive)
                {
                    setActive(button == activeButton);
                }
            }

            _activeNavButton = activeButton;
        }

        // Navigation event handlers
        private void navDashboard_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navDashboard);
            var control = new DashboardControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("لوحة التحكم", "نظرة عامة على النظام");
        }

        private void navPatients_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navPatients);
            var control = new PatientManagementControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("إدارة المراجعين", "إضافة وتعديل بيانات المراجعين");
        }

        private void navDoctors_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navDoctors);
            var control = new DoctorManagementControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("إدارة الأطباء", "إضافة وتعديل بيانات الأطباء");
        }

        private void navServices_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navServices);
            var control = new MedicalServicesControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("الخدمات الطبية", "إدارة الخدمات والتخصصات");
        }

        private void navPayments_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navPayments);
            var control = new PaymentManagementControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("إدارة المدفوعات", "متابعة المدفوعات والفواتير");
        }

        private void navReports_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navReports);
            var control = new ReportsControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("التقارير", "تقارير شاملة عن النظام");
        }

        private void navSettings_Click(object sender, EventArgs e)
        {
            SetActiveNavButton(navSettings);
            var control = new SettingsControl(_databaseService);
            ShowUserControl(control);
            UpdatePageTitle("الإعدادات", "إعدادات النظام والمستخدمين");
        }

        private void UpdatePageTitle(string title, string subtitle)
        {
            pageTitle.Text = title;
            pageSubtitle.Text = subtitle;
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btnMaximize_Click(object sender, EventArgs e)
        {
            this.WindowState = this.WindowState == FormWindowState.Maximized
                ? FormWindowState.Normal
                : FormWindowState.Maximized;
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                this.Hide();
                var loginForm = new LoginForm(_databaseService);
                loginForm.Show();
            }
        }

        // Timer for status updates
        private void statusTimer_Tick(object sender, EventArgs e)
        {
            statusTime.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
            statusUser.Text = $"المستخدم: {_currentUser}";
        }
    }
}