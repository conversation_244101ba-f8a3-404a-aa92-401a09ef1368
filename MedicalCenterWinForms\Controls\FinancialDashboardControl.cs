using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Threading.Tasks;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class FinancialDashboardControl : BaseUserControl
    {
        private FinancialReportService _reportService;
        private System.Windows.Forms.Timer _refreshTimer;

        // UI Components
        private Panel mainPanel;
        private Panel summaryPanel;
        private Panel chartsPanel;
        private Panel kpiPanel;
        private Panel alertsPanel;

        // Summary Cards
        private Panel cardTodayRevenue;
        private Panel cardWeekRevenue;
        private Panel cardMonthRevenue;
        private Panel cardYearRevenue;
        private Panel cardTodayPayments;
        private Panel cardActivePatients;

        // Charts - Placeholder for future implementation
        private Panel revenueChartPanel;
        private Panel paymentsChartPanel;

        // KPI Components
        private Label lblRevenueGrowth;
        private Label lblPaymentGrowth;
        private Label lblAverageTransaction;
        private Label lblTopDoctor;
        private Label lblTopService;
        private ProgressBar pbMonthlyTarget;

        // Alerts
        private ListBox lstAlerts;
        private Label lblLastUpdate;

        public FinancialDashboardControl() : base()
        {
            InitializeComponent();
            InitializeServices();
            InitializeTimer();
            LoadDashboard();
        }

        public FinancialDashboardControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializeServices();
            InitializeTimer();
            LoadDashboard();
        }

        private void InitializeServices()
        {
            _reportService = new FinancialReportService(DatabaseService);
        }

        private void InitializeTimer()
        {
            _refreshTimer = new System.Windows.Forms.Timer
            {
                Interval = 300000 // 5 minutes
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateSummaryPanel();
            CreateChartsPanel();
            CreateKPIPanel();
            CreateAlertsPanel();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                "لوحة المعلومات المالية", true);
            mainPanel.Size = new Size(1180, 780);
            mainPanel.Location = new Point(10, 10);
            this.Controls.Add(mainPanel);
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(1140, 120),
                BackColor = Color.Transparent
            };

            // Today Revenue Card
            cardTodayRevenue = CreateSummaryCard("إيرادات اليوم", "0.00 ريال", "📈", 
                MaterialDesignHelper.Colors.Success, new Point(10, 10));

            // Week Revenue Card
            cardWeekRevenue = CreateSummaryCard("إيرادات الأسبوع", "0.00 ريال", "📊", 
                MaterialDesignHelper.Colors.Primary, new Point(200, 10));

            // Month Revenue Card
            cardMonthRevenue = CreateSummaryCard("إيرادات الشهر", "0.00 ريال", "💰", 
                MaterialDesignHelper.Colors.Info, new Point(390, 10));

            // Year Revenue Card
            cardYearRevenue = CreateSummaryCard("إيرادات السنة", "0.00 ريال", "🏆", 
                MaterialDesignHelper.Colors.Warning, new Point(580, 10));

            // Today Payments Card
            cardTodayPayments = CreateSummaryCard("مدفوعات اليوم", "0", "💳", 
                MaterialDesignHelper.Colors.Secondary, new Point(770, 10));

            // Active Patients Card
            cardActivePatients = CreateSummaryCard("المراجعين النشطين", "0", "👥", 
                MaterialDesignHelper.Colors.Accent, new Point(960, 10));

            summaryPanel.Controls.AddRange(new Control[]
            {
                cardTodayRevenue, cardWeekRevenue, cardMonthRevenue, 
                cardYearRevenue, cardTodayPayments, cardActivePatients
            });

            mainPanel.Controls.Add(summaryPanel);
        }

        private Panel CreateSummaryCard(string title, string value, string icon, Color color, Point location)
        {
            var card = new Panel
            {
                Location = location,
                Size = new Size(180, 100),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            MaterialDesignHelper.AddShadow(card);

            var lblIcon = new Label
            {
                Text = icon,
                Location = new Point(10, 10),
                Size = new Size(30, 30),
                Font = new Font("Segoe UI Emoji", 16F),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter
            };

            var lblTitle = new Label
            {
                Text = title,
                Location = new Point(45, 10),
                Size = new Size(125, 20),
                Font = ArabicFontHelper.GetArabicFont(8F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.TopRight
            };

            var lblValue = new Label
            {
                Text = value,
                Location = new Point(10, 35),
                Size = new Size(160, 35),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter,
                Name = "ValueLabel"
            };

            var lblChange = new Label
            {
                Text = "+0.0%",
                Location = new Point(10, 75),
                Size = new Size(160, 20),
                Font = ArabicFontHelper.GetArabicFont(7F),
                ForeColor = MaterialDesignHelper.Colors.Success,
                TextAlign = ContentAlignment.BottomCenter,
                Name = "ChangeLabel"
            };

            card.Controls.AddRange(new Control[] { lblIcon, lblTitle, lblValue, lblChange });
            return card;
        }

        private void CreateChartsPanel()
        {
            chartsPanel = new Panel
            {
                Location = new Point(20, 190),
                Size = new Size(1140, 300),
                BackColor = Color.Transparent
            };

            // Revenue Trend Chart Placeholder
            revenueChartPanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(560, 280),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var revenueTitle = new Label
            {
                Text = "اتجاه الإيرادات - آخر 7 أيام",
                Location = new Point(10, 10),
                Size = new Size(540, 30),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary,
                TextAlign = ContentAlignment.MiddleCenter
            };

            var revenueInfo = new Label
            {
                Text = "الرسوم البيانية ستكون متاحة في الإصدار القادم",
                Location = new Point(10, 120),
                Size = new Size(540, 40),
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.MiddleCenter
            };

            revenueChartPanel.Controls.AddRange(new Control[] { revenueTitle, revenueInfo });

            // Payments Distribution Chart Placeholder
            paymentsChartPanel = new Panel
            {
                Location = new Point(580, 10),
                Size = new Size(550, 280),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var paymentsTitle = new Label
            {
                Text = "توزيع أنواع المدفوعات",
                Location = new Point(10, 10),
                Size = new Size(530, 30),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary,
                TextAlign = ContentAlignment.MiddleCenter
            };

            var paymentsInfo = new Label
            {
                Text = "الرسوم البيانية ستكون متاحة في الإصدار القادم",
                Location = new Point(10, 120),
                Size = new Size(530, 40),
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.MiddleCenter
            };

            paymentsChartPanel.Controls.AddRange(new Control[] { paymentsTitle, paymentsInfo });

            chartsPanel.Controls.AddRange(new Control[] { revenueChartPanel, paymentsChartPanel });
            mainPanel.Controls.Add(chartsPanel);
        }

        private void CreateKPIPanel()
        {
            kpiPanel = new Panel
            {
                Location = new Point(20, 500),
                Size = new Size(750, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblKPITitle = new Label
            {
                Text = "مؤشرات الأداء الرئيسية",
                Location = new Point(10, 10),
                Size = new Size(200, 25),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary
            };

            lblRevenueGrowth = new Label
            {
                Text = "نمو الإيرادات: +0.0%",
                Location = new Point(10, 40),
                Size = new Size(180, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            lblPaymentGrowth = new Label
            {
                Text = "نمو المدفوعات: +0.0%",
                Location = new Point(200, 40),
                Size = new Size(180, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            lblAverageTransaction = new Label
            {
                Text = "متوسط المعاملة: 0 ريال",
                Location = new Point(390, 40),
                Size = new Size(180, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            lblTopDoctor = new Label
            {
                Text = "أفضل طبيب: -",
                Location = new Point(10, 70),
                Size = new Size(180, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            lblTopService = new Label
            {
                Text = "أفضل خدمة: -",
                Location = new Point(200, 70),
                Size = new Size(180, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            var lblMonthlyTarget = new Label
            {
                Text = "الهدف الشهري:",
                Location = new Point(390, 70),
                Size = new Size(80, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            pbMonthlyTarget = new ProgressBar
            {
                Location = new Point(480, 70),
                Size = new Size(150, 20),
                Style = ProgressBarStyle.Continuous,
                ForeColor = MaterialDesignHelper.Colors.Success
            };

            kpiPanel.Controls.AddRange(new Control[]
            {
                lblKPITitle, lblRevenueGrowth, lblPaymentGrowth, lblAverageTransaction,
                lblTopDoctor, lblTopService, lblMonthlyTarget, pbMonthlyTarget
            });

            mainPanel.Controls.Add(kpiPanel);
        }

        private void CreateAlertsPanel()
        {
            alertsPanel = new Panel
            {
                Location = new Point(780, 500),
                Size = new Size(380, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblAlertsTitle = new Label
            {
                Text = "التنبيهات والإشعارات",
                Location = new Point(10, 10),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary
            };

            lstAlerts = new ListBox
            {
                Location = new Point(10, 40),
                Size = new Size(360, 50),
                Font = ArabicFontHelper.GetArabicFont(8F),
                BorderStyle = BorderStyle.None,
                BackColor = Color.FromArgb(250, 250, 250)
            };

            lblLastUpdate = new Label
            {
                Text = "آخر تحديث: " + DateTime.Now.ToString("HH:mm:ss"),
                Location = new Point(10, 95),
                Size = new Size(200, 20),
                Font = ArabicFontHelper.GetArabicFont(7F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            var btnRefresh = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnRefresh.Location = new Point(340, 95);
            btnRefresh.Size = new Size(30, 20);
            btnRefresh.Click += BtnRefresh_Click;

            alertsPanel.Controls.AddRange(new Control[]
            {
                lblAlertsTitle, lstAlerts, lblLastUpdate, btnRefresh
            });

            mainPanel.Controls.Add(alertsPanel);
        }

        // Data Loading Methods
        private async void LoadDashboard()
        {
            try
            {
                SetLoadingState(true);

                await LoadSummaryCards();
                await LoadCharts();
                await LoadKPIs();
                await LoadAlerts();

                lblLastUpdate.Text = "آخر تحديث: " + DateTime.Now.ToString("HH:mm:ss");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل لوحة المعلومات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task LoadSummaryCards()
        {
            var today = DateTime.Today;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(today.Year, today.Month, 1);
            var yearStart = new DateTime(today.Year, 1, 1);

            // Load daily summary
            var todaySummary = await _reportService.GetDailyFinancialSummary(today);
            var yesterdaySummary = await _reportService.GetDailyFinancialSummary(today.AddDays(-1));

            // Load weekly summary
            var weekSummary = await _reportService.GetFinancialTrendsReport(weekStart, today);
            var lastWeekSummary = await _reportService.GetFinancialTrendsReport(weekStart.AddDays(-7), weekStart.AddDays(-1));

            // Load monthly summary
            var monthSummary = await _reportService.GetMonthlyFinancialSummary(today.Year, today.Month);
            var lastMonthSummary = today.Month > 1 ?
                await _reportService.GetMonthlyFinancialSummary(today.Year, today.Month - 1) :
                await _reportService.GetMonthlyFinancialSummary(today.Year - 1, 12);

            // Load yearly summary
            var yearSummary = await _reportService.GetFinancialTrendsReport(yearStart, today);
            var lastYearSummary = await _reportService.GetFinancialTrendsReport(yearStart.AddYears(-1), yearStart.AddDays(-1));

            // Update cards
            UpdateSummaryCard(cardTodayRevenue, todaySummary.TotalRevenue,
                CalculatePercentageChange(todaySummary.TotalRevenue, yesterdaySummary.TotalRevenue));

            UpdateSummaryCard(cardWeekRevenue, weekSummary.TotalRevenue,
                CalculatePercentageChange(weekSummary.TotalRevenue, lastWeekSummary.TotalRevenue));

            UpdateSummaryCard(cardMonthRevenue, monthSummary.TotalRevenue,
                CalculatePercentageChange(monthSummary.TotalRevenue, lastMonthSummary.TotalRevenue));

            UpdateSummaryCard(cardYearRevenue, yearSummary.TotalRevenue,
                CalculatePercentageChange(yearSummary.TotalRevenue, lastYearSummary.TotalRevenue));

            UpdateSummaryCard(cardTodayPayments, todaySummary.TotalPayments,
                CalculatePercentageChange(todaySummary.TotalPayments, yesterdaySummary.TotalPayments), false);

            // Active patients (simplified calculation)
            var activePatients = todaySummary.MainPaymentDetails.Select(mp => mp.PatientVisit.PatientId)
                .Union(todaySummary.ReferralPaymentDetails.Select(rp => rp.PatientVisit.PatientId))
                .Distinct().Count();

            UpdateSummaryCard(cardActivePatients, activePatients, 0, false);
        }

        private async Task LoadCharts()
        {
            // Charts will be implemented in future version
            // For now, just update the placeholder text with some data

            var today = DateTime.Today;
            var todaySummary = await _reportService.GetDailyFinancialSummary(today);

            // Update revenue chart info
            var revenueInfo = revenueChartPanel.Controls.OfType<Label>().LastOrDefault();
            if (revenueInfo != null)
            {
                revenueInfo.Text = $"إيرادات اليوم: {todaySummary.TotalRevenue:C}\nالرسوم البيانية ستكون متاحة قريباً";
            }

            // Update payments chart info
            var paymentsInfo = paymentsChartPanel.Controls.OfType<Label>().LastOrDefault();
            if (paymentsInfo != null)
            {
                paymentsInfo.Text = $"مدفوعات رئيسية: {todaySummary.MainPaymentsCount}\nمدفوعات تحويل: {todaySummary.ReferralPaymentsCount}\nالرسوم البيانية ستكون متاحة قريباً";
            }
        }

        private async Task LoadKPIs()
        {
            var today = DateTime.Today;
            var monthStart = new DateTime(today.Year, today.Month, 1);
            var lastMonthStart = monthStart.AddMonths(-1);
            var lastMonthEnd = monthStart.AddDays(-1);

            // Get current and last month data
            var currentMonth = await _reportService.GetMonthlyFinancialSummary(today.Year, today.Month);
            var lastMonth = await _reportService.GetMonthlyFinancialSummary(lastMonthStart.Year, lastMonthStart.Month);

            // Revenue growth
            var revenueGrowth = CalculatePercentageChange(currentMonth.TotalRevenue, lastMonth.TotalRevenue);
            lblRevenueGrowth.Text = $"نمو الإيرادات: {revenueGrowth:+0.0;-0.0;0.0}%";
            lblRevenueGrowth.ForeColor = revenueGrowth >= 0 ? MaterialDesignHelper.Colors.Success : MaterialDesignHelper.Colors.Error;

            // Payment growth
            var paymentGrowth = CalculatePercentageChange(currentMonth.TotalPaymentsCount, lastMonth.TotalPaymentsCount);
            lblPaymentGrowth.Text = $"نمو المدفوعات: {paymentGrowth:+0.0;-0.0;0.0}%";
            lblPaymentGrowth.ForeColor = paymentGrowth >= 0 ? MaterialDesignHelper.Colors.Success : MaterialDesignHelper.Colors.Error;

            // Average transaction
            var avgTransaction = currentMonth.TotalPaymentsCount > 0 ?
                currentMonth.TotalRevenue / currentMonth.TotalPaymentsCount : 0;
            lblAverageTransaction.Text = $"متوسط المعاملة: {avgTransaction:C}";

            // Top doctor and service
            var startDate = monthStart;
            var endDate = today;

            var topDoctors = await _reportService.GetTopPerformingDoctors(startDate, endDate, 1);
            if (topDoctors.Any())
            {
                lblTopDoctor.Text = $"أفضل طبيب: {topDoctors.First().Doctor.FullName}";
            }

            var topServices = await _reportService.GetServicePerformanceReport(startDate, endDate);
            if (topServices.Any())
            {
                lblTopService.Text = $"أفضل خدمة: {topServices.First().Service.ServiceName}";
            }

            // Monthly target (example: 50,000 SAR)
            var monthlyTarget = 50000m;
            var targetProgress = currentMonth.TotalRevenue / monthlyTarget * 100;
            pbMonthlyTarget.Value = Math.Min((int)targetProgress, 100);
        }

        private async Task LoadAlerts()
        {
            lstAlerts.Items.Clear();

            var today = DateTime.Today;
            var todaySummary = await _reportService.GetDailyFinancialSummary(today);
            var yesterdaySummary = await _reportService.GetDailyFinancialSummary(today.AddDays(-1));

            // Revenue alerts
            if (todaySummary.TotalRevenue < yesterdaySummary.TotalRevenue * 0.8m)
            {
                lstAlerts.Items.Add("⚠️ انخفاض الإيرادات اليومية بأكثر من 20%");
            }

            if (todaySummary.TotalPayments == 0)
            {
                lstAlerts.Items.Add("🔴 لا توجد مدفوعات اليوم");
            }
            else if (todaySummary.TotalPayments < 5)
            {
                lstAlerts.Items.Add("🟡 عدد قليل من المدفوعات اليوم");
            }

            // Monthly target alert
            var monthStart = new DateTime(today.Year, today.Month, 1);
            var monthSummary = await _reportService.GetMonthlyFinancialSummary(today.Year, today.Month);
            var monthlyTarget = 50000m;
            var daysInMonth = DateTime.DaysInMonth(today.Year, today.Month);
            var daysPassed = today.Day;
            var expectedRevenue = monthlyTarget * daysPassed / daysInMonth;

            if (monthSummary.TotalRevenue < expectedRevenue * 0.8m)
            {
                lstAlerts.Items.Add("📉 الإيرادات الشهرية أقل من المتوقع");
            }

            if (lstAlerts.Items.Count == 0)
            {
                lstAlerts.Items.Add("✅ لا توجد تنبيهات");
            }
        }

        // Helper Methods
        private decimal CalculatePercentageChange(decimal current, decimal previous)
        {
            if (previous == 0) return current > 0 ? 100 : 0;
            return ((current - previous) / previous) * 100;
        }

        private void UpdateSummaryCard(Panel card, decimal value, decimal changePercentage, bool isCurrency = true)
        {
            var lblValue = card.Controls.OfType<Label>().FirstOrDefault(l => l.Name == "ValueLabel");
            var lblChange = card.Controls.OfType<Label>().FirstOrDefault(l => l.Name == "ChangeLabel");

            if (lblValue != null)
            {
                lblValue.Text = isCurrency ? value.ToString("C") : value.ToString("N0");
            }

            if (lblChange != null)
            {
                lblChange.Text = $"{changePercentage:+0.0;-0.0;0.0}%";
                lblChange.ForeColor = changePercentage >= 0 ?
                    MaterialDesignHelper.Colors.Success : MaterialDesignHelper.Colors.Error;
            }
        }

        // Event Handlers
        private async void RefreshTimer_Tick(object sender, EventArgs e)
        {
            await LoadDashboard();
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDashboard();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _refreshTimer?.Stop();
                _refreshTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
