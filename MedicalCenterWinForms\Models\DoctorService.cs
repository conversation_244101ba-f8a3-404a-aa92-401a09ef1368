using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterWinForms.Models
{
    public class DoctorService
    {
        public int DoctorServiceId { get; set; }

        [Required]
        public int DoctorId { get; set; }

        [Required]
        public int MedicalServiceId { get; set; }

        [StringLength(20)]
        public string LinkType { get; set; } = string.Empty; // Direct / Referral

        public bool HasPercentage { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? Percentage { get; set; }

        // Navigation Properties
        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        [ForeignKey("MedicalServiceId")]
        public virtual MedicalService MedicalService { get; set; } = null!;
    }
}
