using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class PaymentManagementControl : BaseUserControl
    {
        // UI Components
        private Panel topPanel;
        private Panel contentPanel;
        private DataGridView dgvPayments;
        private TextBox txtSearch;
        private ComboBox cmbPaymentType;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Button btnAddMainPayment;
        private Button btnAddReferralPayment;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnExport;
        private Label lblSearch;
        private Label lblPaymentType;
        private Label lblDateRange;
        private Label lblTotalAmount;
        private Label lblTotalCount;

        public PaymentManagementControl() : base()
        {
            InitializeComponent();
            LoadPayments();
        }

        public PaymentManagementControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            LoadPayments();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configure main control
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(20);
            this.RightToLeft = RightToLeft.Yes;

            InitializeTopPanel();
            InitializeContentPanel();
            InitializeDataGrid();

            this.ResumeLayout(false);
        }

        private void InitializeTopPanel()
        {
            this.topPanel = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            MaterialDesignHelper.AddShadow(topPanel);

            // Search section
            this.lblSearch = new Label
            {
                Text = "🔍 البحث:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 15)
            };

            this.txtSearch = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(20, 40),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "البحث برقم المراجع أو الاسم..."
            };

            // Payment type filter
            this.lblPaymentType = new Label
            {
                Text = "نوع الدفع:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(290, 15)
            };

            this.cmbPaymentType = new ComboBox
            {
                Size = new Size(150, 30),
                Location = new Point(290, 40),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            cmbPaymentType.Items.AddRange(new[] { "الكل", "نقدي", "بطاقة ائتمان", "تحويل بنكي", "شيك" });
            cmbPaymentType.SelectedIndex = 0;

            // Date range filter
            this.lblDateRange = new Label
            {
                Text = "الفترة الزمنية:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(460, 15)
            };

            this.dtpFromDate = new DateTimePicker
            {
                Size = new Size(120, 30),
                Location = new Point(460, 40),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            this.dtpToDate = new DateTimePicker
            {
                Size = new Size(120, 30),
                Location = new Point(590, 40),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            // Action buttons
            this.btnAddMainPayment = ModernMedicalTheme.Components.CreateAdvancedButton(
                "💰 مدفوعة رئيسية", ModernMedicalTheme.Components.ButtonStyle.Success);
            this.btnAddReferralPayment = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄 مدفوعة تحويل", ModernMedicalTheme.Components.ButtonStyle.Primary);
            this.btnEdit = ModernMedicalTheme.Components.CreateAdvancedButton(
                "✏️ تعديل", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            this.btnDelete = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🗑️ حذف", ModernMedicalTheme.Components.ButtonStyle.Error);
            this.btnRefresh = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄 تحديث", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            this.btnExport = ModernMedicalTheme.Components.CreateAdvancedButton(
                "📊 تصدير", ModernMedicalTheme.Components.ButtonStyle.Ghost);

            btnAddMainPayment.Location = new Point(20, 80);
            btnAddMainPayment.Size = new Size(120, 35);
            btnAddMainPayment.Click += BtnAddMainPayment_Click;

            btnAddReferralPayment.Location = new Point(150, 80);
            btnAddReferralPayment.Size = new Size(120, 35);
            btnAddReferralPayment.Click += BtnAddReferralPayment_Click;

            btnEdit.Location = new Point(280, 80);
            btnEdit.Size = new Size(80, 35);
            btnEdit.Click += BtnEdit_Click;

            btnDelete.Location = new Point(370, 80);
            btnDelete.Size = new Size(80, 35);
            btnDelete.Click += BtnDelete_Click;

            btnRefresh.Location = new Point(460, 80);
            btnRefresh.Size = new Size(80, 35);
            btnRefresh.Click += BtnRefresh_Click;

            btnExport.Location = new Point(550, 80);
            btnExport.Size = new Size(80, 35);
            btnExport.Click += BtnExport_Click;

            // Summary labels
            this.lblTotalCount = new Label
            {
                Text = "إجمالي المدفوعات: 0",
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                AutoSize = true,
                Location = new Point(720, 85)
            };

            this.lblTotalAmount = new Label
            {
                Text = "المبلغ الإجمالي: 0 ر.س",
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Success,
                AutoSize = true,
                Location = new Point(720, 105)
            };

            topPanel.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, lblPaymentType, cmbPaymentType,
                lblDateRange, dtpFromDate, dtpToDate,
                btnAddMainPayment, btnAddReferralPayment, btnEdit, btnDelete, btnRefresh, btnExport,
                lblTotalCount, lblTotalAmount
            });

            this.Controls.Add(topPanel);
        }

        private void InitializeContentPanel()
        {
            this.contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 10, 0, 0)
            };

            MaterialDesignHelper.AddShadow(contentPanel);
            this.Controls.Add(contentPanel);
        }

        private void InitializeDataGrid()
        {
            this.dgvPayments = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Configure header style
            dgvPayments.ColumnHeadersDefaultCellStyle.BackColor = MaterialDesignHelper.Colors.Primary;
            dgvPayments.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvPayments.ColumnHeadersDefaultCellStyle.Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold);
            dgvPayments.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvPayments.ColumnHeadersHeight = 45;

            // Configure row style
            dgvPayments.DefaultCellStyle.BackColor = Color.White;
            dgvPayments.DefaultCellStyle.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            dgvPayments.DefaultCellStyle.SelectionBackColor = MaterialDesignHelper.Colors.Primary;
            dgvPayments.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvPayments.RowTemplate.Height = 40;

            // Add columns
            dgvPayments.Columns.Add("Id", "رقم الدفعة");
            dgvPayments.Columns.Add("PatientName", "اسم المراجع");
            dgvPayments.Columns.Add("ServiceName", "الخدمة");
            dgvPayments.Columns.Add("Amount", "المبلغ (ر.س)");
            dgvPayments.Columns.Add("PaymentType", "نوع الدفع");
            dgvPayments.Columns.Add("PaymentDate", "تاريخ الدفع");
            dgvPayments.Columns.Add("Status", "الحالة");
            dgvPayments.Columns.Add("Notes", "ملاحظات");

            // Configure column widths
            dgvPayments.Columns["Id"].Width = 100;
            dgvPayments.Columns["PatientName"].Width = 150;
            dgvPayments.Columns["ServiceName"].Width = 150;
            dgvPayments.Columns["Amount"].Width = 120;
            dgvPayments.Columns["PaymentType"].Width = 100;
            dgvPayments.Columns["PaymentDate"].Width = 120;
            dgvPayments.Columns["Status"].Width = 80;
            dgvPayments.Columns["Notes"].Width = 200;

            contentPanel.Controls.Add(dgvPayments);
        }

        private async void LoadPayments()
        {
            try
            {
                SetLoadingState(true);

                using var context = DatabaseService.GetDbContext();

                // Load Main Payments
                var mainPayments = await context.MainPayments
                    .Include(mp => mp.PatientVisit)
                        .ThenInclude(pv => pv.Patient)
                    .Include(mp => mp.PatientVisit)
                        .ThenInclude(pv => pv.Doctor)
                    .OrderByDescending(mp => mp.PaymentDate)
                    .ToListAsync();

                // Load Referral Payments
                var referralPayments = await context.ReferralPayments
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Patient)
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Doctor)
                    .Include(rp => rp.MedicalService)
                    .OrderByDescending(rp => rp.PaymentDate)
                    .ToListAsync();

                dgvPayments.Rows.Clear();
                decimal totalAmount = 0;

                // Add Main Payments
                foreach (var payment in mainPayments)
                {
                    var totalFees = payment.ConsultationFee + payment.ExamFee;
                    dgvPayments.Rows.Add(
                        $"MP{payment.MainPaymentId:D4}",
                        payment.PatientVisit.Patient.PatientName,
                        "مدفوعة رئيسية",
                        totalFees.ToString("C"),
                        "رئيسي",
                        payment.PaymentDate.ToString("yyyy-MM-dd"),
                        "مكتمل",
                        payment.Notes ?? ""
                    );
                    totalAmount += totalFees;
                }

                // Add Referral Payments
                foreach (var payment in referralPayments)
                {
                    dgvPayments.Rows.Add(
                        $"RP{payment.ReferralPaymentId:D4}",
                        payment.PatientVisit.Patient.PatientName,
                        payment.MedicalService.ServiceName,
                        payment.Amount.ToString("C"),
                        "تحويل",
                        payment.PaymentDate.ToString("yyyy-MM-dd"),
                        "مكتمل",
                        payment.Notes ?? ""
                    );
                    totalAmount += payment.Amount;
                }

                lblTotalCount.Text = $"إجمالي المدفوعات: {dgvPayments.Rows.Count}";
                lblTotalAmount.Text = $"المبلغ الإجمالي: {totalAmount:C}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        // Event Handlers
        private void BtnAddMainPayment_Click(object sender, EventArgs e)
        {
            var editControl = new MainPaymentEditControl(DatabaseService);
            editControl.MainPaymentSaved += (s, args) => LoadPayments();
            ShowEditDialog(editControl, "إضافة مدفوعة رئيسية");
        }

        private void BtnAddReferralPayment_Click(object sender, EventArgs e)
        {
            var editControl = new ReferralPaymentEditControl(DatabaseService);
            editControl.ReferralPaymentSaved += (s, args) => LoadPayments();
            ShowEditDialog(editControl, "إضافة مدفوعة تحويل");
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvPayments.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار مدفوعة للتعديل");
                return;
            }

            var selectedRow = dgvPayments.SelectedRows[0];
            var paymentId = selectedRow.Cells[0].Value.ToString();

            if (paymentId.StartsWith("MP"))
            {
                // Main Payment
                var id = int.Parse(paymentId.Substring(2));
                EditMainPayment(id);
            }
            else if (paymentId.StartsWith("RP"))
            {
                // Referral Payment
                var id = int.Parse(paymentId.Substring(2));
                EditReferralPayment(id);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvPayments.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار مدفوعة للحذف");
                return;
            }

            var selectedRow = dgvPayments.SelectedRows[0];
            var paymentId = selectedRow.Cells[0].Value.ToString();
            var patientName = selectedRow.Cells[1].Value.ToString();

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف مدفوعة المراجع '{patientName}'؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                DeletePayment(paymentId);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadPayments();
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("ميزة التصدير ستكون متاحة قريباً", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowEditDialog(UserControl control, string title)
        {
            var dialog = new Form
            {
                Text = title,
                Size = new Size(850, 800),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Font = ArabicFontHelper.GetArabicFont(10F)
            };

            control.Dock = DockStyle.Fill;
            dialog.Controls.Add(control);
            dialog.ShowDialog(this);
        }

        private async void EditMainPayment(int mainPaymentId)
        {
            try
            {
                using var context = DatabaseService.GetDbContext();
                var mainPayment = await context.MainPayments
                    .Include(mp => mp.PatientVisit)
                        .ThenInclude(pv => pv.Patient)
                    .Include(mp => mp.PatientVisit)
                        .ThenInclude(pv => pv.Doctor)
                    .FirstOrDefaultAsync(mp => mp.MainPaymentId == mainPaymentId);

                if (mainPayment != null)
                {
                    var editControl = new MainPaymentEditControl(DatabaseService, mainPayment);
                    editControl.MainPaymentSaved += (s, args) => LoadPayments();
                    ShowEditDialog(editControl, "تعديل مدفوعة رئيسية");
                }
                else
                {
                    ShowError("لم يتم العثور على المدفوعة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات المدفوعة: {ex.Message}");
            }
        }

        private async void EditReferralPayment(int referralPaymentId)
        {
            try
            {
                using var context = DatabaseService.GetDbContext();
                var referralPayment = await context.ReferralPayments
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Patient)
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Doctor)
                    .Include(rp => rp.MedicalService)
                    .FirstOrDefaultAsync(rp => rp.ReferralPaymentId == referralPaymentId);

                if (referralPayment != null)
                {
                    var editControl = new ReferralPaymentEditControl(DatabaseService, referralPayment);
                    editControl.ReferralPaymentSaved += (s, args) => LoadPayments();
                    ShowEditDialog(editControl, "تعديل مدفوعة تحويل");
                }
                else
                {
                    ShowError("لم يتم العثور على المدفوعة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات المدفوعة: {ex.Message}");
            }
        }

        private async void DeletePayment(string paymentId)
        {
            try
            {
                SetLoadingState(true);

                using var context = DatabaseService.GetDbContext();

                if (paymentId.StartsWith("MP"))
                {
                    var id = int.Parse(paymentId.Substring(2));
                    var mainPayment = await context.MainPayments.FindAsync(id);
                    if (mainPayment != null)
                    {
                        context.MainPayments.Remove(mainPayment);
                        await context.SaveChangesAsync();
                        ShowSuccess("تم حذف المدفوعة الرئيسية بنجاح");
                    }
                }
                else if (paymentId.StartsWith("RP"))
                {
                    var id = int.Parse(paymentId.Substring(2));
                    var referralPayment = await context.ReferralPayments.FindAsync(id);
                    if (referralPayment != null)
                    {
                        context.ReferralPayments.Remove(referralPayment);
                        await context.SaveChangesAsync();
                        ShowSuccess("تم حذف مدفوعة التحويل بنجاح");
                    }
                }

                LoadPayments();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حذف المدفوعة: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }
    }
}
