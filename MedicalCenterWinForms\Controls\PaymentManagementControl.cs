using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class PaymentManagementControl : UserControl
    {
        private readonly DatabaseService _databaseService;

        // UI Components
        private Panel topPanel;
        private Panel contentPanel;
        private DataGridView dgvPayments;
        private TextBox txtSearch;
        private ComboBox cmbPaymentType;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnExport;
        private Label lblSearch;
        private Label lblPaymentType;
        private Label lblDateRange;
        private Label lblTotalAmount;
        private Label lblTotalCount;

        public PaymentManagementControl(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            LoadPayments();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configure main control
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(20);
            this.RightToLeft = RightToLeft.Yes;

            InitializeTopPanel();
            InitializeContentPanel();
            InitializeDataGrid();

            this.ResumeLayout(false);
        }

        private void InitializeTopPanel()
        {
            this.topPanel = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            MaterialDesignHelper.AddShadow(topPanel);

            // Search section
            this.lblSearch = new Label
            {
                Text = "🔍 البحث:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 15)
            };

            this.txtSearch = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(20, 40),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "البحث برقم المراجع أو الاسم..."
            };

            // Payment type filter
            this.lblPaymentType = new Label
            {
                Text = "نوع الدفع:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(290, 15)
            };

            this.cmbPaymentType = new ComboBox
            {
                Size = new Size(150, 30),
                Location = new Point(290, 40),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            cmbPaymentType.Items.AddRange(new[] { "الكل", "نقدي", "بطاقة ائتمان", "تحويل بنكي", "شيك" });
            cmbPaymentType.SelectedIndex = 0;

            // Date range filter
            this.lblDateRange = new Label
            {
                Text = "الفترة الزمنية:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(460, 15)
            };

            this.dtpFromDate = new DateTimePicker
            {
                Size = new Size(120, 30),
                Location = new Point(460, 40),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            this.dtpToDate = new DateTimePicker
            {
                Size = new Size(120, 30),
                Location = new Point(590, 40),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            // Action buttons
            this.btnAdd = MaterialDesignHelper.CreateModernButton("➕ دفعة جديدة", MaterialDesignHelper.Colors.Success);
            this.btnEdit = MaterialDesignHelper.CreateModernButton("✏️ تعديل", MaterialDesignHelper.Colors.Primary);
            this.btnDelete = MaterialDesignHelper.CreateModernButton("🗑️ حذف", MaterialDesignHelper.Colors.Danger);
            this.btnRefresh = MaterialDesignHelper.CreateModernButton("🔄 تحديث", MaterialDesignHelper.Colors.Secondary);
            this.btnExport = MaterialDesignHelper.CreateModernButton("📊 تصدير", MaterialDesignHelper.Colors.Info);

            btnAdd.Location = new Point(20, 80);
            btnEdit.Location = new Point(150, 80);
            btnDelete.Location = new Point(280, 80);
            btnRefresh.Location = new Point(410, 80);
            btnExport.Location = new Point(540, 80);

            // Summary labels
            this.lblTotalCount = new Label
            {
                Text = "إجمالي المدفوعات: 0",
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                AutoSize = true,
                Location = new Point(720, 85)
            };

            this.lblTotalAmount = new Label
            {
                Text = "المبلغ الإجمالي: 0 ر.س",
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Success,
                AutoSize = true,
                Location = new Point(720, 105)
            };

            topPanel.Controls.AddRange(new Control[] 
            { 
                lblSearch, txtSearch, lblPaymentType, cmbPaymentType,
                lblDateRange, dtpFromDate, dtpToDate,
                btnAdd, btnEdit, btnDelete, btnRefresh, btnExport,
                lblTotalCount, lblTotalAmount
            });

            this.Controls.Add(topPanel);
        }

        private void InitializeContentPanel()
        {
            this.contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 10, 0, 0)
            };

            MaterialDesignHelper.AddShadow(contentPanel);
            this.Controls.Add(contentPanel);
        }

        private void InitializeDataGrid()
        {
            this.dgvPayments = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Configure header style
            dgvPayments.ColumnHeadersDefaultCellStyle.BackColor = MaterialDesignHelper.Colors.Primary;
            dgvPayments.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvPayments.ColumnHeadersDefaultCellStyle.Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold);
            dgvPayments.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvPayments.ColumnHeadersHeight = 45;

            // Configure row style
            dgvPayments.DefaultCellStyle.BackColor = Color.White;
            dgvPayments.DefaultCellStyle.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            dgvPayments.DefaultCellStyle.SelectionBackColor = MaterialDesignHelper.Colors.Primary;
            dgvPayments.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvPayments.RowTemplate.Height = 40;

            // Add columns
            dgvPayments.Columns.Add("Id", "رقم الدفعة");
            dgvPayments.Columns.Add("PatientName", "اسم المراجع");
            dgvPayments.Columns.Add("ServiceName", "الخدمة");
            dgvPayments.Columns.Add("Amount", "المبلغ (ر.س)");
            dgvPayments.Columns.Add("PaymentType", "نوع الدفع");
            dgvPayments.Columns.Add("PaymentDate", "تاريخ الدفع");
            dgvPayments.Columns.Add("Status", "الحالة");
            dgvPayments.Columns.Add("Notes", "ملاحظات");

            // Configure column widths
            dgvPayments.Columns["Id"].Width = 100;
            dgvPayments.Columns["PatientName"].Width = 150;
            dgvPayments.Columns["ServiceName"].Width = 150;
            dgvPayments.Columns["Amount"].Width = 120;
            dgvPayments.Columns["PaymentType"].Width = 100;
            dgvPayments.Columns["PaymentDate"].Width = 120;
            dgvPayments.Columns["Status"].Width = 80;
            dgvPayments.Columns["Notes"].Width = 200;

            contentPanel.Controls.Add(dgvPayments);
        }

        private void LoadPayments()
        {
            try
            {
                // Sample data - replace with actual database queries
                var samplePayments = new[]
                {
                    new { Id = "PAY001", PatientName = "أحمد محمد علي", ServiceName = "كشف عام", Amount = "150", PaymentType = "نقدي", PaymentDate = "2025/01/20", Status = "مكتمل", Notes = "دفع كامل" },
                    new { Id = "PAY002", PatientName = "فاطمة سالم", ServiceName = "كشف أطفال", Amount = "200", PaymentType = "بطاقة ائتمان", PaymentDate = "2025/01/20", Status = "مكتمل", Notes = "" },
                    new { Id = "PAY003", PatientName = "محمد أحمد", ServiceName = "تحليل دم", Amount = "80", PaymentType = "نقدي", PaymentDate = "2025/01/19", Status = "مكتمل", Notes = "تحليل شامل" },
                    new { Id = "PAY004", PatientName = "سارة خالد", ServiceName = "أشعة سينية", Amount = "100", PaymentType = "تحويل بنكي", PaymentDate = "2025/01/19", Status = "معلق", Notes = "في انتظار التأكيد" },
                    new { Id = "PAY005", PatientName = "عبدالله سعد", ServiceName = "كشف عيون", Amount = "180", PaymentType = "نقدي", PaymentDate = "2025/01/18", Status = "مكتمل", Notes = "" },
                    new { Id = "PAY006", PatientName = "نورا أحمد", ServiceName = "كشف نساء", Amount = "250", PaymentType = "بطاقة ائتمان", PaymentDate = "2025/01/18", Status = "مكتمل", Notes = "متابعة حمل" },
                    new { Id = "PAY007", PatientName = "خالد محمد", ServiceName = "علاج طبيعي", Amount = "100", PaymentType = "نقدي", PaymentDate = "2025/01/17", Status = "مكتمل", Notes = "جلسة أولى" },
                    new { Id = "PAY008", PatientName = "ريم سالم", ServiceName = "كشف أسنان", Amount = "120", PaymentType = "شيك", PaymentDate = "2025/01/17", Status = "معلق", Notes = "شيك مؤجل" }
                };

                dgvPayments.Rows.Clear();
                decimal totalAmount = 0;

                foreach (var payment in samplePayments)
                {
                    dgvPayments.Rows.Add(
                        payment.Id,
                        payment.PatientName,
                        payment.ServiceName,
                        payment.Amount,
                        payment.PaymentType,
                        payment.PaymentDate,
                        payment.Status,
                        payment.Notes
                    );

                    if (decimal.TryParse(payment.Amount, out decimal amount))
                    {
                        totalAmount += amount;
                    }
                }

                lblTotalCount.Text = $"إجمالي المدفوعات: {dgvPayments.Rows.Count}";
                lblTotalAmount.Text = $"المبلغ الإجمالي: {totalAmount:N0} ر.س";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
