using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class MainPaymentEditControl : BaseUserControl
    {
        private MainPayment? _mainPayment;
        private bool _isEditMode;
        private PaymentCalculationService _paymentCalculationService;

        // UI Components
        private Panel mainPanel;
        private Panel buttonPanel;
        private ComboBox cmbPatient;
        private ComboBox cmbDoctor;
        private DateTimePicker dtpPaymentDate;
        private NumericUpDown nudConsultationFee;
        private NumericUpDown nudExamFee;
        private NumericUpDown nudDoctorShare;
        private NumericUpDown nudCenterShare;
        private TextBox txtNotes;
        private Button btnSave;
        private Button btnCancel;
        private Button btnCalculateShares;
        private CheckBox chkAutoCalculate;

        // Labels
        private Label lblPatient;
        private Label lblDoctor;
        private Label lblPaymentDate;
        private Label lblConsultationFee;
        private Label lblExamFee;
        private Label lblDoctorShare;
        private Label lblCenterShare;
        private Label lblTotalAmount;
        private Label lblNotes;
        private Label lblCalculationInfo;

        public event EventHandler? MainPaymentSaved;

        public MainPaymentEditControl() : base()
        {
            InitializeComponent();
            InitializePaymentService();
            _isEditMode = false;
        }

        public MainPaymentEditControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            _isEditMode = false;
            LoadComboBoxData();
        }

        public MainPaymentEditControl(DatabaseService databaseService, MainPayment mainPayment) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            _mainPayment = mainPayment;
            _isEditMode = true;
            LoadComboBoxData();
            LoadMainPaymentData();
        }

        private void InitializePaymentService()
        {
            _paymentCalculationService = new PaymentCalculationService(DatabaseService);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(800, 700);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateButtonPanel();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                _isEditMode ? "تعديل مدفوعة رئيسية" : "إضافة مدفوعة رئيسية", true);
            mainPanel.Size = new Size(780, 620);
            mainPanel.Location = new Point(10, 10);

            int yPos = 50;
            int labelWidth = 120;
            int controlWidth = 200;
            int spacing = 40;

            // Patient selection
            lblPatient = CreateLabel("المراجع:", new Point(620, yPos));
            cmbPatient = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbPatient.Location = new Point(400, yPos);
            cmbPatient.Size = new Size(controlWidth, 30);
            cmbPatient.SelectedIndexChanged += CmbPatient_SelectedIndexChanged;

            yPos += spacing;

            // Doctor selection
            lblDoctor = CreateLabel("الطبيب:", new Point(620, yPos));
            cmbDoctor = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbDoctor.Location = new Point(400, yPos);
            cmbDoctor.Size = new Size(controlWidth, 30);
            cmbDoctor.SelectedIndexChanged += CmbDoctor_SelectedIndexChanged;

            yPos += spacing;

            // Payment Date
            lblPaymentDate = CreateLabel("تاريخ الدفع:", new Point(620, yPos));
            dtpPaymentDate = new DateTimePicker
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 30),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            yPos += spacing;

            // Consultation Fee
            lblConsultationFee = CreateLabel("رسوم الاستشارة:", new Point(620, yPos));
            nudConsultationFee = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2
            };
            nudConsultationFee.ValueChanged += NudFees_ValueChanged;

            yPos += spacing;

            // Exam Fee
            lblExamFee = CreateLabel("رسوم الفحص:", new Point(620, yPos));
            nudExamFee = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2
            };
            nudExamFee.ValueChanged += NudFees_ValueChanged;

            yPos += spacing;

            // Auto Calculate Checkbox
            chkAutoCalculate = new CheckBox
            {
                Text = "حساب تلقائي للحصص",
                Location = new Point(500, yPos),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                Checked = true
            };
            chkAutoCalculate.CheckedChanged += ChkAutoCalculate_CheckedChanged;

            // Calculate Button
            btnCalculateShares = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🧮 احسب الحصص", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnCalculateShares.Location = new Point(350, yPos);
            btnCalculateShares.Size = new Size(120, 30);
            btnCalculateShares.Click += BtnCalculateShares_Click;

            yPos += spacing;

            // Doctor Share
            lblDoctorShare = CreateLabel("حصة الطبيب:", new Point(620, yPos));
            nudDoctorShare = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2,
                ReadOnly = true,
                BackColor = Color.LightGray
            };

            yPos += spacing;

            // Center Share
            lblCenterShare = CreateLabel("حصة المركز:", new Point(620, yPos));
            nudCenterShare = new NumericUpDown
            {
                Location = new Point(400, yPos),
                Size = new Size(controlWidth, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2,
                ReadOnly = true,
                BackColor = Color.LightGray
            };

            yPos += spacing;

            // Total Amount Label
            lblTotalAmount = new Label
            {
                Text = "المبلغ الإجمالي: 0.00 ريال",
                Location = new Point(400, yPos),
                Size = new Size(200, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary,
                TextAlign = ContentAlignment.MiddleLeft
            };

            yPos += spacing;

            // Calculation Info
            lblCalculationInfo = new Label
            {
                Text = "سيتم حساب الحصص تلقائياً بناءً على إعدادات الطبيب",
                Location = new Point(200, yPos),
                Size = new Size(400, 40),
                Font = ArabicFontHelper.GetArabicFont(8F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.TopCenter
            };

            yPos += 60;

            // Notes
            lblNotes = CreateLabel("ملاحظات:", new Point(620, yPos));
            txtNotes = ModernMedicalTheme.Components.CreateAdvancedTextBox("ملاحظات إضافية...");
            txtNotes.Location = new Point(200, yPos);
            txtNotes.Size = new Size(400, 60);
            txtNotes.Multiline = true;

            mainPanel.Controls.AddRange(new Control[]
            {
                lblPatient, cmbPatient, lblDoctor, cmbDoctor, lblPaymentDate, dtpPaymentDate,
                lblConsultationFee, nudConsultationFee, lblExamFee, nudExamFee,
                chkAutoCalculate, btnCalculateShares, lblDoctorShare, nudDoctorShare,
                lblCenterShare, nudCenterShare, lblTotalAmount, lblCalculationInfo,
                lblNotes, txtNotes
            });

            this.Controls.Add(mainPanel);
        }

        private void CreateButtonPanel()
        {
            buttonPanel = new Panel
            {
                Location = new Point(10, 640),
                Size = new Size(780, 50),
                BackColor = Color.Transparent
            };

            btnSave = ModernMedicalTheme.Components.CreateAdvancedButton(
                "💾 حفظ", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnSave.Location = new Point(600, 10);
            btnSave.Size = new Size(80, 35);
            btnSave.Click += BtnSave_Click;

            btnCancel = ModernMedicalTheme.Components.CreateAdvancedButton(
                "❌ إلغاء", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnCancel.Location = new Point(510, 10);
            btnCancel.Size = new Size(80, 35);
            btnCancel.Click += BtnCancel_Click;

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });
            this.Controls.Add(buttonPanel);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(9F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                TextAlign = ContentAlignment.MiddleRight
            };
        }

        private async void LoadComboBoxData()
        {
            try
            {
                using var context = DatabaseService.GetDbContext();

                // Load patient visits
                var patientVisits = await context.PatientVisits
                    .Include(pv => pv.Patient)
                    .Include(pv => pv.Doctor)
                    .OrderByDescending(pv => pv.VisitDate)
                    .Take(100) // Load recent 100 patient visits
                    .ToListAsync();

                cmbPatient.Items.Clear();
                foreach (var visit in patientVisits)
                {
                    cmbPatient.Items.Add(new {
                        Text = $"{visit.Patient.PatientName} - {visit.VisitDate:yyyy-MM-dd}",
                        Value = visit.PatientVisitId
                    });
                }
                cmbPatient.DisplayMember = "Text";
                cmbPatient.ValueMember = "Value";

                // Load doctors
                var doctors = await context.Doctors
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();

                cmbDoctor.Items.Clear();
                foreach (var doctor in doctors)
                {
                    cmbDoctor.Items.Add(new { Text = doctor.FullName, Value = doctor.DoctorId });
                }
                cmbDoctor.DisplayMember = "Text";
                cmbDoctor.ValueMember = "Value";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private void LoadMainPaymentData()
        {
            if (_mainPayment == null) return;

            try
            {
                // Set patient visit
                for (int i = 0; i < cmbPatient.Items.Count; i++)
                {
                    dynamic item = cmbPatient.Items[i];
                    if (item.Value == _mainPayment.PatientVisitId)
                    {
                        cmbPatient.SelectedIndex = i;
                        break;
                    }
                }

                // Set doctor (from patient visit)
                if (_mainPayment.PatientVisit?.DoctorId != null)
                {
                    for (int i = 0; i < cmbDoctor.Items.Count; i++)
                    {
                        dynamic item = cmbDoctor.Items[i];
                        if (item.Value == _mainPayment.PatientVisit.DoctorId)
                        {
                            cmbDoctor.SelectedIndex = i;
                            break;
                        }
                    }
                }

                // Set other properties
                dtpPaymentDate.Value = _mainPayment.PaymentDate;
                nudConsultationFee.Value = _mainPayment.ConsultationFee;
                nudExamFee.Value = _mainPayment.ExamFee;
                nudDoctorShare.Value = _mainPayment.DoctorShare;
                nudCenterShare.Value = _mainPayment.CenterShare;
                txtNotes.Text = _mainPayment.Notes ?? "";

                UpdateTotalAmount();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات المدفوعة: {ex.Message}");
            }
        }

        // Event Handlers
        private async void CmbPatient_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (chkAutoCalculate.Checked)
            {
                await CalculateShares();
            }
        }

        private async void CmbDoctor_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (chkAutoCalculate.Checked)
            {
                await CalculateShares();
            }
        }

        private async void NudFees_ValueChanged(object sender, EventArgs e)
        {
            UpdateTotalAmount();
            if (chkAutoCalculate.Checked)
            {
                await CalculateShares();
            }
        }

        private void ChkAutoCalculate_CheckedChanged(object sender, EventArgs e)
        {
            nudDoctorShare.ReadOnly = chkAutoCalculate.Checked;
            nudCenterShare.ReadOnly = chkAutoCalculate.Checked;

            if (chkAutoCalculate.Checked)
            {
                nudDoctorShare.BackColor = Color.LightGray;
                nudCenterShare.BackColor = Color.LightGray;
                lblCalculationInfo.Text = "سيتم حساب الحصص تلقائياً بناءً على إعدادات الطبيب";
            }
            else
            {
                nudDoctorShare.BackColor = Color.White;
                nudCenterShare.BackColor = Color.White;
                lblCalculationInfo.Text = "يمكنك تعديل الحصص يدوياً";
            }
        }

        private async void BtnCalculateShares_Click(object sender, EventArgs e)
        {
            await CalculateShares();
        }

        private void UpdateTotalAmount()
        {
            var total = nudConsultationFee.Value + nudExamFee.Value;
            lblTotalAmount.Text = $"المبلغ الإجمالي: {total:C}";
        }

        private async Task CalculateShares()
        {
            try
            {
                if (cmbDoctor.SelectedValue == null)
                {
                    lblCalculationInfo.Text = "يرجى اختيار الطبيب أولاً";
                    lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Error;
                    return;
                }

                var doctorId = (int)cmbDoctor.SelectedValue;
                var consultationFee = nudConsultationFee.Value;
                var examFee = nudExamFee.Value;

                var (doctorShare, centerShare) = await _paymentCalculationService
                    .CalculateMainPaymentShares(doctorId, consultationFee, examFee);

                nudDoctorShare.Value = doctorShare;
                nudCenterShare.Value = centerShare;

                lblCalculationInfo.Text = $"تم حساب الحصص بناءً على إعدادات الطبيب";
                lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Success;
            }
            catch (Exception ex)
            {
                lblCalculationInfo.Text = $"خطأ في حساب الحصص: {ex.Message}";
                lblCalculationInfo.ForeColor = MaterialDesignHelper.Colors.Error;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
            {
                return;
            }

            try
            {
                SetLoadingState(true);
                btnSave.Text = "جاري الحفظ...";

                using var context = DatabaseService.GetDbContext();

                if (_isEditMode && _mainPayment != null)
                {
                    var existingPayment = await context.MainPayments.FindAsync(_mainPayment.MainPaymentId);
                    if (existingPayment != null)
                    {
                        UpdateMainPaymentFromForm(existingPayment);
                    }
                }
                else
                {
                    var newPayment = new MainPayment();
                    UpdateMainPaymentFromForm(newPayment);
                    context.MainPayments.Add(newPayment);
                }

                await context.SaveChangesAsync();

                ShowSuccess(_isEditMode ? "تم تحديث المدفوعة بنجاح" : "تم إضافة المدفوعة بنجاح");
                MainPaymentSaved?.Invoke(this, EventArgs.Empty);

                // Close the dialog
                this.ParentForm?.Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
                btnSave.Text = "💾 حفظ";
            }
        }

        private void UpdateMainPaymentFromForm(MainPayment payment)
        {
            if (!_isEditMode)
            {
                payment.PaymentDate = dtpPaymentDate.Value;
            }

            payment.PatientVisitId = (int)cmbPatient.SelectedValue;
            payment.ConsultationFee = nudConsultationFee.Value;
            payment.ExamFee = nudExamFee.Value;
            payment.DoctorShare = nudDoctorShare.Value;
            payment.CenterShare = nudCenterShare.Value;
            payment.Notes = txtNotes.Text.Trim();
        }

        private bool ValidateInput()
        {
            if (cmbPatient.SelectedValue == null)
            {
                ShowError("يرجى اختيار زيارة المراجع");
                cmbPatient.Focus();
                return false;
            }

            if (nudConsultationFee.Value <= 0 && nudExamFee.Value <= 0)
            {
                ShowError("يجب أن يكون هناك رسوم استشارة أو فحص على الأقل");
                nudConsultationFee.Focus();
                return false;
            }

            var totalFees = nudConsultationFee.Value + nudExamFee.Value;
            var totalShares = nudDoctorShare.Value + nudCenterShare.Value;

            if (Math.Abs(totalFees - totalShares) > 0.01m)
            {
                ShowError($"مجموع الحصص ({totalShares:C}) لا يساوي مجموع الرسوم ({totalFees:C})");
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.ParentForm?.Close();
        }
    }
}
