﻿#pragma checksum "..\..\..\..\Pages\BackupRestorePage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "67D1CAAACB3D7DF48731ED39AA453D5769A5E181"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// BackupRestorePage
    /// </summary>
    public partial class BackupRestorePage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 36 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StatisticsButton;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScheduleButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BackupTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseBackupPathButton;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeAttachmentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeSettingsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeLogsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompressBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EncryptBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox BackupPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CompressionLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateFullBackupButton;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateIncrementalBackupButton;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateDifferentialBackupButton;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card ProgressCard;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar BackupProgressBar;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressTextBlock;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBackupsButton;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BackupsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RestoreModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox RestorePasswordBox;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CreateBackupBeforeRestoreCheckBox;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox VerifyIntegrityCheckBox;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestRestoreButton;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ValidateBackupButton;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreButton;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CleanupDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 432 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleanupButton;
        
        #line default
        #line hidden
        
        
        #line 450 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 459 "..\..\..\..\Pages\BackupRestorePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/backuprestorepage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\BackupRestorePage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatisticsButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.StatisticsButton.Click += new System.Windows.RoutedEventHandler(this.StatisticsButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ScheduleButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.ScheduleButton.Click += new System.Windows.RoutedEventHandler(this.ScheduleButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BackupTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BrowseBackupPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.BrowseBackupPathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseBackupPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BackupDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.IncludeAttachmentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.IncludeSettingsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.IncludeLogsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.CompressBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.EncryptBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.BackupPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 13:
            this.CompressionLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.CreateFullBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 200 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.CreateFullBackupButton.Click += new System.Windows.RoutedEventHandler(this.CreateFullBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CreateIncrementalBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 210 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.CreateIncrementalBackupButton.Click += new System.Windows.RoutedEventHandler(this.CreateIncrementalBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CreateDifferentialBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.CreateDifferentialBackupButton.Click += new System.Windows.RoutedEventHandler(this.CreateDifferentialBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ProgressCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 18:
            this.BackupProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 19:
            this.ProgressTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.RefreshBackupsButton = ((System.Windows.Controls.Button)(target));
            
            #line 281 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.RefreshBackupsButton.Click += new System.Windows.RoutedEventHandler(this.RefreshBackupsButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.BackupsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 22:
            this.RestoreModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.RestorePasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 24:
            this.CreateBackupBeforeRestoreCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.VerifyIntegrityCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.TestRestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 366 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.TestRestoreButton.Click += new System.Windows.RoutedEventHandler(this.TestRestoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.ValidateBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 376 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.ValidateBackupButton.Click += new System.Windows.RoutedEventHandler(this.ValidateBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.RestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 386 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.RestoreButton.Click += new System.Windows.RoutedEventHandler(this.RestoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.CleanupDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 30:
            this.CleanupButton = ((System.Windows.Controls.Button)(target));
            
            #line 435 "..\..\..\..\Pages\BackupRestorePage.xaml"
            this.CleanupButton.Click += new System.Windows.RoutedEventHandler(this.CleanupButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.LoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 32:
            this.LoadingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

