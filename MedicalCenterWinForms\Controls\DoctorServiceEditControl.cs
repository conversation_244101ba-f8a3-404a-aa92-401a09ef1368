using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class DoctorServiceEditControl : BaseUserControl
    {
        private DoctorService? _doctorService;
        private bool _isEditMode;
        private PaymentCalculationService _paymentCalculationService;

        // UI Components
        private Panel mainPanel;
        private Panel buttonPanel;
        private ComboBox cmbDoctor;
        private ComboBox cmbMedicalService;
        private ComboBox cmbLinkType;
        private CheckBox chkHasPercentage;
        private NumericUpDown nudPercentage;
        private CheckBox chkIsFixedAmount;
        private NumericUpDown nudFixedAmount;
        private NumericUpDown nudDoctorDefaultPrice;
        private NumericUpDown nudServiceCost;
        private CheckBox chkIsActive;
        private TextBox txtNotes;
        private Button btnSave;
        private Button btnCancel;
        private Button btnCalculateTest;

        // Labels
        private Label lblDoctor;
        private Label lblMedicalService;
        private Label lblLinkType;
        private Label lblPercentage;
        private Label lblFixedAmount;
        private Label lblDoctorDefaultPrice;
        private Label lblServiceCost;
        private Label lblNotes;
        private Label lblCalculationResult;

        public event EventHandler? DoctorServiceSaved;

        public DoctorServiceEditControl() : base()
        {
            InitializeComponent();
            InitializePaymentService();
            _isEditMode = false;
        }

        public DoctorServiceEditControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            _isEditMode = false;
            LoadComboBoxData();
        }

        public DoctorServiceEditControl(DatabaseService databaseService, DoctorService doctorService) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            _doctorService = doctorService;
            _isEditMode = true;
            LoadComboBoxData();
            LoadDoctorServiceData();
        }

        private void InitializePaymentService()
        {
            _paymentCalculationService = new PaymentCalculationService(DatabaseService);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(780, 580);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateButtonPanel();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                _isEditMode ? "تعديل ربط الطبيب بالخدمة" : "إضافة ربط طبيب بخدمة", true);
            mainPanel.Size = new Size(760, 520);
            mainPanel.Location = new Point(10, 10);

            int yPos = 50;
            int labelWidth = 120;
            int controlWidth = 200;
            int spacing = 40;

            // Doctor selection
            lblDoctor = CreateLabel("الطبيب:", new Point(600, yPos));
            cmbDoctor = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbDoctor.Location = new Point(380, yPos);
            cmbDoctor.Size = new Size(controlWidth, 30);
            cmbDoctor.SelectedIndexChanged += CmbDoctor_SelectedIndexChanged;

            yPos += spacing;

            // Medical Service selection
            lblMedicalService = CreateLabel("الخدمة الطبية:", new Point(600, yPos));
            cmbMedicalService = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbMedicalService.Location = new Point(380, yPos);
            cmbMedicalService.Size = new Size(controlWidth, 30);
            cmbMedicalService.SelectedIndexChanged += CmbMedicalService_SelectedIndexChanged;

            yPos += spacing;

            // Link Type
            lblLinkType = CreateLabel("نوع الربط:", new Point(600, yPos));
            cmbLinkType = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbLinkType.Location = new Point(380, yPos);
            cmbLinkType.Size = new Size(controlWidth, 30);
            cmbLinkType.Items.AddRange(new object[]
            {
                new { Text = "مباشر", Value = "Direct" },
                new { Text = "تحويل", Value = "Referral" }
            });
            cmbLinkType.DisplayMember = "Text";
            cmbLinkType.ValueMember = "Value";

            yPos += spacing;

            // Has Percentage
            chkHasPercentage = new CheckBox
            {
                Text = "له نسبة مئوية",
                Location = new Point(500, yPos),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary
            };
            chkHasPercentage.CheckedChanged += ChkHasPercentage_CheckedChanged;

            lblPercentage = CreateLabel("النسبة %:", new Point(350, yPos));
            nudPercentage = new NumericUpDown
            {
                Location = new Point(250, yPos),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2,
                Enabled = false
            };

            yPos += spacing;

            // Is Fixed Amount
            chkIsFixedAmount = new CheckBox
            {
                Text = "مبلغ مقطوع",
                Location = new Point(500, yPos),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary
            };
            chkIsFixedAmount.CheckedChanged += ChkIsFixedAmount_CheckedChanged;

            lblFixedAmount = CreateLabel("المبلغ المقطوع:", new Point(350, yPos));
            nudFixedAmount = new NumericUpDown
            {
                Location = new Point(200, yPos),
                Size = new Size(130, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2,
                Enabled = false
            };

            yPos += spacing;

            // Doctor Default Price
            lblDoctorDefaultPrice = CreateLabel("السعر الافتراضي للطبيب:", new Point(600, yPos));
            nudDoctorDefaultPrice = new NumericUpDown
            {
                Location = new Point(380, yPos),
                Size = new Size(130, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2
            };

            yPos += spacing;

            // Service Cost
            lblServiceCost = CreateLabel("تكلفة الخدمة:", new Point(600, yPos));
            nudServiceCost = new NumericUpDown
            {
                Location = new Point(380, yPos),
                Size = new Size(130, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2
            };

            yPos += spacing;

            // Is Active
            chkIsActive = new CheckBox
            {
                Text = "نشط",
                Location = new Point(500, yPos),
                Size = new Size(80, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                Checked = true
            };

            yPos += spacing;

            // Notes
            lblNotes = CreateLabel("ملاحظات:", new Point(600, yPos));
            txtNotes = ModernMedicalTheme.Components.CreateAdvancedTextBox("ملاحظات إضافية...");
            txtNotes.Location = new Point(200, yPos);
            txtNotes.Size = new Size(380, 60);
            txtNotes.Multiline = true;

            yPos += 80;

            // Calculation Test Button
            btnCalculateTest = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🧮 اختبار الحساب", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnCalculateTest.Location = new Point(500, yPos);
            btnCalculateTest.Size = new Size(120, 30);
            btnCalculateTest.Click += BtnCalculateTest_Click;

            // Calculation Result Label
            lblCalculationResult = new Label
            {
                Text = "نتيجة الاختبار ستظهر هنا",
                Location = new Point(200, yPos + 5),
                Size = new Size(280, 20),
                Font = ArabicFontHelper.GetArabicFont(8F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            mainPanel.Controls.AddRange(new Control[]
            {
                lblDoctor, cmbDoctor, lblMedicalService, cmbMedicalService, lblLinkType, cmbLinkType,
                chkHasPercentage, lblPercentage, nudPercentage, chkIsFixedAmount, lblFixedAmount, nudFixedAmount,
                lblDoctorDefaultPrice, nudDoctorDefaultPrice, lblServiceCost, nudServiceCost,
                chkIsActive, lblNotes, txtNotes, btnCalculateTest, lblCalculationResult
            });

            this.Controls.Add(mainPanel);
        }

        private void CreateButtonPanel()
        {
            buttonPanel = new Panel
            {
                Location = new Point(10, 540),
                Size = new Size(760, 50),
                BackColor = Color.Transparent
            };

            btnSave = ModernMedicalTheme.Components.CreateAdvancedButton(
                "💾 حفظ", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnSave.Location = new Point(580, 10);
            btnSave.Size = new Size(80, 35);
            btnSave.Click += BtnSave_Click;

            btnCancel = ModernMedicalTheme.Components.CreateAdvancedButton(
                "❌ إلغاء", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnCancel.Location = new Point(490, 10);
            btnCancel.Size = new Size(80, 35);
            btnCancel.Click += BtnCancel_Click;

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });
            this.Controls.Add(buttonPanel);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(9F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                TextAlign = ContentAlignment.MiddleRight
            };
        }

        private async void LoadComboBoxData()
        {
            try
            {
                using var context = DatabaseService.GetDbContext();

                // Load doctors
                var doctors = await context.Doctors
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();

                cmbDoctor.Items.Clear();
                foreach (var doctor in doctors)
                {
                    cmbDoctor.Items.Add(new { Text = doctor.FullName, Value = doctor.DoctorId });
                }
                cmbDoctor.DisplayMember = "Text";
                cmbDoctor.ValueMember = "Value";

                // Load medical services
                var services = await context.MedicalServices
                    .OrderBy(s => s.ServiceName)
                    .ToListAsync();

                cmbMedicalService.Items.Clear();
                foreach (var service in services)
                {
                    cmbMedicalService.Items.Add(new { Text = service.ServiceName, Value = service.MedicalServiceId });
                }
                cmbMedicalService.DisplayMember = "Text";
                cmbMedicalService.ValueMember = "Value";

                // Set default link type
                if (cmbLinkType.Items.Count > 0)
                {
                    cmbLinkType.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private void LoadDoctorServiceData()
        {
            if (_doctorService == null) return;

            try
            {
                // Set doctor
                for (int i = 0; i < cmbDoctor.Items.Count; i++)
                {
                    dynamic item = cmbDoctor.Items[i];
                    if (item.Value == _doctorService.DoctorId)
                    {
                        cmbDoctor.SelectedIndex = i;
                        break;
                    }
                }

                // Set medical service
                for (int i = 0; i < cmbMedicalService.Items.Count; i++)
                {
                    dynamic item = cmbMedicalService.Items[i];
                    if (item.Value == _doctorService.MedicalServiceId)
                    {
                        cmbMedicalService.SelectedIndex = i;
                        break;
                    }
                }

                // Set link type
                for (int i = 0; i < cmbLinkType.Items.Count; i++)
                {
                    dynamic item = cmbLinkType.Items[i];
                    if (item.Value.ToString() == _doctorService.LinkType)
                    {
                        cmbLinkType.SelectedIndex = i;
                        break;
                    }
                }

                // Set other properties
                chkHasPercentage.Checked = _doctorService.HasPercentage;
                if (_doctorService.Percentage.HasValue)
                {
                    nudPercentage.Value = _doctorService.Percentage.Value;
                }

                chkIsFixedAmount.Checked = _doctorService.IsFixedAmount;
                if (_doctorService.FixedAmount.HasValue)
                {
                    nudFixedAmount.Value = _doctorService.FixedAmount.Value;
                }

                if (_doctorService.DoctorDefaultPrice.HasValue)
                {
                    nudDoctorDefaultPrice.Value = _doctorService.DoctorDefaultPrice.Value;
                }

                if (_doctorService.ServiceCost.HasValue)
                {
                    nudServiceCost.Value = _doctorService.ServiceCost.Value;
                }

                chkIsActive.Checked = _doctorService.IsActive;
                txtNotes.Text = _doctorService.Notes;

                // Disable doctor and service selection in edit mode
                cmbDoctor.Enabled = false;
                cmbMedicalService.Enabled = false;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات الربط: {ex.Message}");
            }
        }

        // Event Handlers
        private void ChkHasPercentage_CheckedChanged(object sender, EventArgs e)
        {
            nudPercentage.Enabled = chkHasPercentage.Checked;
            if (!chkHasPercentage.Checked)
            {
                nudPercentage.Value = 0;
            }

            // Disable fixed amount if percentage is selected
            if (chkHasPercentage.Checked)
            {
                chkIsFixedAmount.Checked = false;
            }
        }

        private void ChkIsFixedAmount_CheckedChanged(object sender, EventArgs e)
        {
            nudFixedAmount.Enabled = chkIsFixedAmount.Checked;
            if (!chkIsFixedAmount.Checked)
            {
                nudFixedAmount.Value = 0;
            }

            // Disable percentage if fixed amount is selected
            if (chkIsFixedAmount.Checked)
            {
                chkHasPercentage.Checked = false;
            }
        }

        private async void CmbDoctor_SelectedIndexChanged(object sender, EventArgs e)
        {
            await UpdateCalculationResult();
        }

        private async void CmbMedicalService_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadServiceDefaultPrice();
            await UpdateCalculationResult();
        }

        private async Task LoadServiceDefaultPrice()
        {
            try
            {
                if (cmbMedicalService.SelectedValue != null)
                {
                    var serviceId = (int)cmbMedicalService.SelectedValue;
                    using var context = DatabaseService.GetDbContext();
                    var service = await context.MedicalServices.FindAsync(serviceId);

                    if (service?.DefaultPrice.HasValue == true && nudDoctorDefaultPrice.Value == 0)
                    {
                        nudDoctorDefaultPrice.Value = service.DefaultPrice.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل السعر الافتراضي: {ex.Message}");
            }
        }

        private async void BtnCalculateTest_Click(object sender, EventArgs e)
        {
            await UpdateCalculationResult();
        }

        private async Task UpdateCalculationResult()
        {
            try
            {
                if (cmbDoctor.SelectedValue == null || cmbMedicalService.SelectedValue == null)
                {
                    lblCalculationResult.Text = "يرجى اختيار الطبيب والخدمة أولاً";
                    return;
                }

                var doctorId = (int)cmbDoctor.SelectedValue;
                var serviceId = (int)cmbMedicalService.SelectedValue;
                var testAmount = nudDoctorDefaultPrice.Value > 0 ? nudDoctorDefaultPrice.Value : 1000m;

                // Create temporary doctor service for calculation
                var tempDoctorService = new DoctorService
                {
                    DoctorId = doctorId,
                    MedicalServiceId = serviceId,
                    HasPercentage = chkHasPercentage.Checked,
                    Percentage = chkHasPercentage.Checked ? nudPercentage.Value : null,
                    IsFixedAmount = chkIsFixedAmount.Checked,
                    FixedAmount = chkIsFixedAmount.Checked ? nudFixedAmount.Value : null,
                    ServiceCost = nudServiceCost.Value > 0 ? nudServiceCost.Value : null
                };

                decimal doctorShare = 0;
                decimal centerShare = testAmount;

                if (tempDoctorService.IsFixedAmount && tempDoctorService.FixedAmount.HasValue)
                {
                    doctorShare = tempDoctorService.FixedAmount.Value;
                }
                else if (tempDoctorService.HasPercentage && tempDoctorService.Percentage.HasValue)
                {
                    if (tempDoctorService.ServiceCost.HasValue)
                    {
                        var netAmount = testAmount - tempDoctorService.ServiceCost.Value;
                        if (netAmount > 0)
                        {
                            doctorShare = netAmount * (tempDoctorService.Percentage.Value / 100);
                        }
                    }
                    else
                    {
                        doctorShare = testAmount * (tempDoctorService.Percentage.Value / 100);
                    }
                }

                centerShare = testAmount - doctorShare;

                lblCalculationResult.Text = $"مثال: مبلغ {testAmount:C} → طبيب: {doctorShare:C}, مركز: {centerShare:C}";
                lblCalculationResult.ForeColor = MaterialDesignHelper.Colors.Success;
            }
            catch (Exception ex)
            {
                lblCalculationResult.Text = $"خطأ في الحساب: {ex.Message}";
                lblCalculationResult.ForeColor = MaterialDesignHelper.Colors.Error;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
            {
                return;
            }

            try
            {
                SetLoadingState(true);
                btnSave.Text = "جاري الحفظ...";

                using var context = DatabaseService.GetDbContext();

                if (_isEditMode && _doctorService != null)
                {
                    var existingDoctorService = await context.DoctorServices.FindAsync(_doctorService.DoctorServiceId);
                    if (existingDoctorService != null)
                    {
                        UpdateDoctorServiceFromForm(existingDoctorService);
                    }
                }
                else
                {
                    // Check if this combination already exists
                    var doctorId = (int)cmbDoctor.SelectedValue;
                    var serviceId = (int)cmbMedicalService.SelectedValue;

                    var existingService = await context.DoctorServices
                        .FirstOrDefaultAsync(ds => ds.DoctorId == doctorId && ds.MedicalServiceId == serviceId);

                    if (existingService != null)
                    {
                        ShowError("هذا الطبيب مرتبط بالفعل بهذه الخدمة");
                        return;
                    }

                    var newDoctorService = new DoctorService();
                    UpdateDoctorServiceFromForm(newDoctorService);
                    context.DoctorServices.Add(newDoctorService);
                }

                await context.SaveChangesAsync();

                ShowSuccess(_isEditMode ? "تم تحديث الربط بنجاح" : "تم إضافة الربط بنجاح");
                DoctorServiceSaved?.Invoke(this, EventArgs.Empty);

                // Close the dialog
                this.ParentForm?.Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
                btnSave.Text = "💾 حفظ";
            }
        }

        private void UpdateDoctorServiceFromForm(DoctorService doctorService)
        {
            if (!_isEditMode)
            {
                doctorService.DoctorId = (int)cmbDoctor.SelectedValue;
                doctorService.MedicalServiceId = (int)cmbMedicalService.SelectedValue;
                doctorService.CreatedDate = DateTime.Now;
            }

            doctorService.LinkType = cmbLinkType.SelectedValue?.ToString() ?? "Direct";
            doctorService.HasPercentage = chkHasPercentage.Checked;
            doctorService.Percentage = chkHasPercentage.Checked ? nudPercentage.Value : null;
            doctorService.IsFixedAmount = chkIsFixedAmount.Checked;
            doctorService.FixedAmount = chkIsFixedAmount.Checked ? nudFixedAmount.Value : null;
            doctorService.DoctorDefaultPrice = nudDoctorDefaultPrice.Value > 0 ? nudDoctorDefaultPrice.Value : null;
            doctorService.ServiceCost = nudServiceCost.Value > 0 ? nudServiceCost.Value : null;
            doctorService.IsActive = chkIsActive.Checked;
            doctorService.Notes = txtNotes.Text.Trim();
        }

        private bool ValidateInput()
        {
            if (cmbDoctor.SelectedValue == null)
            {
                ShowError("يرجى اختيار الطبيب");
                cmbDoctor.Focus();
                return false;
            }

            if (cmbMedicalService.SelectedValue == null)
            {
                ShowError("يرجى اختيار الخدمة الطبية");
                cmbMedicalService.Focus();
                return false;
            }

            if (cmbLinkType.SelectedValue == null)
            {
                ShowError("يرجى اختيار نوع الربط");
                cmbLinkType.Focus();
                return false;
            }

            if (chkHasPercentage.Checked && nudPercentage.Value <= 0)
            {
                ShowError("يرجى تحديد نسبة صحيحة");
                nudPercentage.Focus();
                return false;
            }

            if (chkIsFixedAmount.Checked && nudFixedAmount.Value <= 0)
            {
                ShowError("يرجى تحديد مبلغ مقطوع صحيح");
                nudFixedAmount.Focus();
                return false;
            }

            if (chkHasPercentage.Checked && chkIsFixedAmount.Checked)
            {
                ShowError("لا يمكن اختيار النسبة والمبلغ المقطوع معاً");
                return false;
            }

            if (!chkHasPercentage.Checked && !chkIsFixedAmount.Checked)
            {
                ShowError("يجب اختيار إما النسبة أو المبلغ المقطوع");
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.ParentForm?.Close();
        }
    }
}
