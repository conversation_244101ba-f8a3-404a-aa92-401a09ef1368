using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;

namespace MedicalCenterWinForms.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Doctor> Doctors { get; set; }
        public DbSet<MedicalService> MedicalServices { get; set; }
        public DbSet<DoctorService> DoctorServices { get; set; }
        public DbSet<PatientVisit> PatientVisits { get; set; }
        public DbSet<MainPayment> MainPayments { get; set; }
        public DbSet<ReferralPayment> ReferralPayments { get; set; }
        public DbSet<UserAccount> UserAccounts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Doctor Configuration
            modelBuilder.Entity<Doctor>(entity =>
            {
                entity.HasKey(e => e.DoctorId);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Specialty).HasMaxLength(100);
            });

            // MedicalService Configuration
            modelBuilder.Entity<MedicalService>(entity =>
            {
                entity.HasKey(e => e.MedicalServiceId);
                entity.Property(e => e.ServiceName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ServiceType).HasMaxLength(20);
                entity.Property(e => e.DefaultPrice).HasColumnType("decimal(18,2)");
            });

            // DoctorService Configuration
            modelBuilder.Entity<DoctorService>(entity =>
            {
                entity.HasKey(e => e.DoctorServiceId);
                entity.Property(e => e.LinkType).HasMaxLength(20);
                entity.Property(e => e.Percentage).HasColumnType("decimal(5,2)");

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.DoctorServices)
                    .HasForeignKey(d => d.DoctorId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.MedicalService)
                    .WithMany(p => p.DoctorServices)
                    .HasForeignKey(d => d.MedicalServiceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // PatientVisit Configuration
            modelBuilder.Entity<PatientVisit>(entity =>
            {
                entity.HasKey(e => e.PatientVisitId);
                entity.Property(e => e.PatientName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Diagnosis).HasMaxLength(255);
                entity.Property(e => e.Province).HasMaxLength(100);
                entity.Property(e => e.BookingStaff).HasMaxLength(100);
                entity.Property(e => e.VisitCountLabel).HasMaxLength(50);
                entity.Property(e => e.PhoneNumber).HasMaxLength(20);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.PatientVisits)
                    .HasForeignKey(d => d.DoctorId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Unique constraint for VisitNumber per Doctor per Day
                entity.HasIndex(e => new { e.DoctorId, e.VisitDate, e.VisitNumber })
                    .IsUnique();
            });

            // MainPayment Configuration
            modelBuilder.Entity<MainPayment>(entity =>
            {
                entity.HasKey(e => e.MainPaymentId);
                entity.Property(e => e.ConsultationFee).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ExamFee).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DoctorShare).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CenterShare).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CashierName).HasMaxLength(100);

                entity.HasOne(d => d.PatientVisit)
                    .WithOne(p => p.MainPayment)
                    .HasForeignKey<MainPayment>(d => d.PatientVisitId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // ReferralPayment Configuration
            modelBuilder.Entity<ReferralPayment>(entity =>
            {
                entity.HasKey(e => e.ReferralPaymentId);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DoctorShare).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CenterShare).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Section).HasMaxLength(100);
                entity.Property(e => e.CashierName).HasMaxLength(100);

                entity.HasOne(d => d.PatientVisit)
                    .WithMany(p => p.ReferralPayments)
                    .HasForeignKey(d => d.PatientVisitId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.MedicalService)
                    .WithMany(p => p.ReferralPayments)
                    .HasForeignKey(d => d.MedicalServiceId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // UserAccount Configuration
            modelBuilder.Entity<UserAccount>(entity =>
            {
                entity.HasKey(e => e.UserAccountId);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.HashedPassword).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);

                entity.HasIndex(e => e.Username).IsUnique();
            });
        }
    }
}
