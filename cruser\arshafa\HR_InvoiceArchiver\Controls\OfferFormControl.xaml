<UserControl x:Class="HR_InvoiceArchiver.Controls.OfferFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">
    <Border Style="{StaticResource ModernCardStyle}" Margin="0,0,0,12">
        <StackPanel>
            <TextBlock Text="إضافة عرض جديد" FontSize="20" FontWeight="Bold" Foreground="#1976D2" Margin="0,0,0,12"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <TextBox x:Name="ScientificOfficeTextBox" Width="160" Margin="0,0,12,0" materialDesign:HintAssist.Hint="المكتب العلمي"/>
                <TextBox x:Name="RepresentativeNameTextBox" Width="140" Margin="0,0,12,0" materialDesign:HintAssist.Hint="اسم المندوب"/>
                <TextBox x:Name="RepresentativePhoneTextBox" Width="120" Margin="0,0,12,0" materialDesign:HintAssist.Hint="رقم المندوب"/>
                <!-- Auto-suggest for ScientificName -->
                <ComboBox x:Name="ScientificNameComboBox" Width="160" IsEditable="True" materialDesign:HintAssist.Hint="المادة العلمية"/>
                <TextBox x:Name="TradeNameTextBox" Width="140" Margin="0,0,12,0" materialDesign:HintAssist.Hint="المادة التجارية"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <TextBox x:Name="PriceTextBox" Width="100" Margin="0,0,12,0" materialDesign:HintAssist.Hint="السعر"/>
                <TextBox x:Name="BonusOrDiscountTextBox" Width="120" Margin="0,0,12,0" materialDesign:HintAssist.Hint="البونص أو الخصم"/>
                <TextBox x:Name="NotesTextBox" Width="200" Margin="0,0,12,0" materialDesign:HintAssist.Hint="ملاحظات"/>
                <Button x:Name="AttachmentButton" Content="رفع مرفق" Width="100"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <Button x:Name="AddOfferButton" Content="إضافة العرض" Style="{StaticResource ModernPrimaryButtonStyle}" Width="120" Margin="0,0,12,0"/>
                <Button x:Name="ClearButton" Content="تفريغ الحقول" Style="{StaticResource ModernSecondaryButtonStyle}" Width="120"/>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl> 