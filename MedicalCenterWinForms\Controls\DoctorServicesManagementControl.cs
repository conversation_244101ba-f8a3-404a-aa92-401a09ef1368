using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class DoctorServicesManagementControl : BaseUserControl
    {
        private List<DoctorService> _doctorServices = new List<DoctorService>();
        private PaymentCalculationService _paymentCalculationService;

        // UI Components
        private Panel mainPanel;
        private Panel filterPanel;
        private Panel actionPanel;
        private DataGridView dgvDoctorServices;
        private ComboBox cmbFilterDoctor;
        private ComboBox cmbFilterService;
        private ComboBox cmbFilterLinkType;
        private CheckBox chkShowActiveOnly;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnBulkUpdate;
        private Button btnExport;
        private Button btnClearFilters;
        private Label lblTotalRecords;
        private Label lblActiveRecords;

        public DoctorServicesManagementControl() : base()
        {
            InitializeComponent();
            InitializePaymentService();
            LoadDoctorServices();
        }

        public DoctorServicesManagementControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializePaymentService();
            LoadDoctorServices();
        }

        private void InitializePaymentService()
        {
            _paymentCalculationService = new PaymentCalculationService(DatabaseService);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateFilterPanel();
            CreateActionPanel();
            CreateDataGrid();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                "إدارة ربط الأطباء بالخدمات", true);
            mainPanel.Size = new Size(1180, 780);
            mainPanel.Location = new Point(10, 10);
            this.Controls.Add(mainPanel);
        }

        private void CreateFilterPanel()
        {
            filterPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(1140, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Doctor Filter
            var lblFilterDoctor = new Label
            {
                Text = "فلترة بالطبيب:",
                Location = new Point(10, 10),
                Size = new Size(80, 25),
                Font = ArabicFontHelper.GetArabicFont(9F, FontStyle.Bold)
            };

            cmbFilterDoctor = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbFilterDoctor.Location = new Point(100, 10);
            cmbFilterDoctor.Size = new Size(150, 25);
            cmbFilterDoctor.SelectedIndexChanged += FilterChanged;

            // Service Filter
            var lblFilterService = new Label
            {
                Text = "فلترة بالخدمة:",
                Location = new Point(260, 10),
                Size = new Size(80, 25),
                Font = ArabicFontHelper.GetArabicFont(9F, FontStyle.Bold)
            };

            cmbFilterService = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbFilterService.Location = new Point(350, 10);
            cmbFilterService.Size = new Size(150, 25);
            cmbFilterService.SelectedIndexChanged += FilterChanged;

            lblSearch = new Label
            {
                Text = "بحث:",
                Location = new Point(480, 50),
                Size = new Size(40, 25),
                Font = ArabicFontHelper.GetArabicFont(9F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary
            };

            txtSearch = ModernMedicalTheme.Components.CreateAdvancedTextBox("بحث في الأطباء والخدمات...");
            txtSearch.Location = new Point(300, 48);
            txtSearch.Size = new Size(170, 30);
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // Action buttons
            btnAdd = ModernMedicalTheme.Components.CreateAdvancedButton(
                "➕ إضافة ربط", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnAdd.Location = new Point(200, 45);
            btnAdd.Size = new Size(90, 35);
            btnAdd.Click += BtnAdd_Click;

            btnEdit = ModernMedicalTheme.Components.CreateAdvancedButton(
                "✏️ تعديل", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnEdit.Location = new Point(105, 45);
            btnEdit.Size = new Size(90, 35);
            btnEdit.Click += BtnEdit_Click;

            btnDelete = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🗑️ حذف", ModernMedicalTheme.Components.ButtonStyle.Error);
            btnDelete.Location = new Point(10, 45);
            btnDelete.Size = new Size(90, 35);
            btnDelete.Click += BtnDelete_Click;

            btnRefresh = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄 تحديث", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnRefresh.Location = new Point(10, 85);
            btnRefresh.Size = new Size(90, 25);
            btnRefresh.Click += BtnRefresh_Click;

            btnClearFilter = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🧹 مسح الفلتر", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnClearFilter.Location = new Point(105, 85);
            btnClearFilter.Size = new Size(90, 25);
            btnClearFilter.Click += BtnClearFilter_Click;

            lblTotalCount = new Label
            {
                Text = "إجمالي الروابط: 0",
                Location = new Point(300, 90),
                Size = new Size(200, 20),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            topPanel.Controls.AddRange(new Control[]
            {
                lblFilterDoctor, cmbFilterDoctor, lblFilterService, cmbFilterService,
                lblSearch, txtSearch, btnAdd, btnEdit, btnDelete, btnRefresh, btnClearFilter, lblTotalCount
            });

            this.Controls.Add(topPanel);
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel
            {
                Location = new Point(10, 140),
                Size = new Size(1180, 650),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            this.Controls.Add(contentPanel);
        }

        private void SetupDataGridView()
        {
            dgvDoctorServices = new DataGridView
            {
                Location = new Point(10, 10),
                Size = new Size(1160, 630),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                RowHeadersVisible = false,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            // Apply modern styling
            ModernMedicalTheme.Components.ApplyDataGridViewTheme(dgvDoctorServices);

            // Add columns
            dgvDoctorServices.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "DoctorServiceId", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "DoctorName", HeaderText = "اسم الطبيب", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ServiceName", HeaderText = "اسم الخدمة", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "LinkType", HeaderText = "نوع الربط", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "HasPercentage", HeaderText = "له نسبة", Width = 70 },
                new DataGridViewTextBoxColumn { Name = "Percentage", HeaderText = "النسبة %", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "IsFixedAmount", HeaderText = "مبلغ مقطوع", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "FixedAmount", HeaderText = "المبلغ المقطوع", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "DoctorDefaultPrice", HeaderText = "السعر الافتراضي", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ServiceCost", HeaderText = "تكلفة الخدمة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "IsActive", HeaderText = "نشط", Width = 60 },
                new DataGridViewTextBoxColumn { Name = "CreatedDate", HeaderText = "تاريخ الإنشاء", Width = 100 }
            });

            dgvDoctorServices.CellDoubleClick += DgvDoctorServices_CellDoubleClick;

            mainPanel.Controls.Add(dgvDoctorServices);
        }

        private async void LoadDoctorServices()
        {
            try
            {
                SetLoadingState(true);

                using var context = DatabaseService.GetDbContext();
                _doctorServices = await context.DoctorServices
                    .Include(ds => ds.Doctor)
                    .Include(ds => ds.MedicalService)
                    .OrderBy(ds => ds.Doctor.FullName)
                    .ThenBy(ds => ds.MedicalService.ServiceName)
                    .ToListAsync();

                await LoadFilterData();
                FilterAndDisplayData();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task LoadFilterData()
        {
            try
            {
                using var context = DatabaseService.GetDbContext();

                // Load doctors
                var doctors = await context.Doctors
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();

                cmbFilterDoctor.Items.Clear();
                cmbFilterDoctor.Items.Add(new { Text = "جميع الأطباء", Value = -1 });
                foreach (var doctor in doctors)
                {
                    cmbFilterDoctor.Items.Add(new { Text = doctor.FullName, Value = doctor.DoctorId });
                }
                cmbFilterDoctor.DisplayMember = "Text";
                cmbFilterDoctor.ValueMember = "Value";
                cmbFilterDoctor.SelectedIndex = 0;

                // Load services
                var services = await context.MedicalServices
                    .OrderBy(s => s.ServiceName)
                    .ToListAsync();

                cmbFilterService.Items.Clear();
                cmbFilterService.Items.Add(new { Text = "جميع الخدمات", Value = -1 });
                foreach (var service in services)
                {
                    cmbFilterService.Items.Add(new { Text = service.ServiceName, Value = service.MedicalServiceId });
                }
                cmbFilterService.DisplayMember = "Text";
                cmbFilterService.ValueMember = "Value";
                cmbFilterService.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات الفلتر: {ex.Message}");
            }
        }

        private void FilterAndDisplayData()
        {
            try
            {
                var filteredData = _doctorServices.AsEnumerable();

                // Apply doctor filter
                if (cmbFilterDoctor.SelectedValue != null && (int)cmbFilterDoctor.SelectedValue != -1)
                {
                    filteredData = filteredData.Where(ds => ds.DoctorId == (int)cmbFilterDoctor.SelectedValue);
                }

                // Apply service filter
                if (cmbFilterService.SelectedValue != null && (int)cmbFilterService.SelectedValue != -1)
                {
                    filteredData = filteredData.Where(ds => ds.MedicalServiceId == (int)cmbFilterService.SelectedValue);
                }

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    var searchTerm = txtSearch.Text.Trim().ToLower();
                    filteredData = filteredData.Where(ds =>
                        ds.Doctor.FullName.ToLower().Contains(searchTerm) ||
                        ds.MedicalService.ServiceName.ToLower().Contains(searchTerm) ||
                        ds.LinkType.ToLower().Contains(searchTerm));
                }

                dgvDoctorServices.Rows.Clear();

                foreach (var ds in filteredData)
                {
                    dgvDoctorServices.Rows.Add(
                        ds.DoctorServiceId,
                        ds.Doctor.FullName,
                        ds.MedicalService.ServiceName,
                        ds.LinkType == "Direct" ? "مباشر" : "تحويل",
                        ds.HasPercentage ? "نعم" : "لا",
                        ds.Percentage?.ToString("F2"),
                        ds.IsFixedAmount ? "نعم" : "لا",
                        ds.FixedAmount?.ToString("C"),
                        ds.DoctorDefaultPrice?.ToString("C"),
                        ds.ServiceCost?.ToString("C"),
                        ds.IsActive ? "نشط" : "غير نشط",
                        ds.CreatedDate.ToString("yyyy-MM-dd")
                    );
                }

                // Update count display if needed
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في عرض البيانات: {ex.Message}");
            }
        }

        // Event Handlers
        private void CmbFilterDoctor_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterAndDisplayData();
        }

        private void CmbFilterService_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterAndDisplayData();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterAndDisplayData();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var editControl = new DoctorServiceEditControl(DatabaseService);
            editControl.DoctorServiceSaved += (s, args) => LoadDoctorServices();
            ShowEditDialog(editControl, "إضافة ربط طبيب بخدمة");
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvDoctorServices.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار ربط للتعديل");
                return;
            }

            var selectedRow = dgvDoctorServices.SelectedRows[0];
            var doctorServiceId = (int)selectedRow.Cells["DoctorServiceId"].Value;
            var doctorService = _doctorServices.FirstOrDefault(ds => ds.DoctorServiceId == doctorServiceId);

            if (doctorService != null)
            {
                var editControl = new DoctorServiceEditControl(DatabaseService, doctorService);
                editControl.DoctorServiceSaved += (s, args) => LoadDoctorServices();
                ShowEditDialog(editControl, "تعديل ربط الطبيب بالخدمة");
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvDoctorServices.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار ربط للحذف");
                return;
            }

            var selectedRow = dgvDoctorServices.SelectedRows[0];
            var doctorServiceId = (int)selectedRow.Cells["DoctorServiceId"].Value;
            var doctorName = selectedRow.Cells["DoctorName"].Value.ToString();
            var serviceName = selectedRow.Cells["ServiceName"].Value.ToString();

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف ربط الطبيب '{doctorName}' بالخدمة '{serviceName}'؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    SetLoadingState(true);

                    using var context = DatabaseService.GetDbContext();
                    var doctorService = await context.DoctorServices.FindAsync(doctorServiceId);
                    if (doctorService != null)
                    {
                        context.DoctorServices.Remove(doctorService);
                        await context.SaveChangesAsync();

                        ShowSuccess("تم حذف الربط بنجاح");
                        LoadDoctorServices();
                    }
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الربط: {ex.Message}");
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDoctorServices();
        }

        private void BtnClearFilter_Click(object sender, EventArgs e)
        {
            cmbFilterDoctor.SelectedIndex = 0;
            cmbFilterService.SelectedIndex = 0;
            txtSearch.Clear();
        }

        private void DgvDoctorServices_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void ShowEditDialog(UserControl control, string title)
        {
            var dialog = new Form
            {
                Text = title,
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Font = ArabicFontHelper.GetArabicFont(10F)
            };

            control.Dock = DockStyle.Fill;
            dialog.Controls.Add(control);
            dialog.ShowDialog(this);
        }
    }
}
