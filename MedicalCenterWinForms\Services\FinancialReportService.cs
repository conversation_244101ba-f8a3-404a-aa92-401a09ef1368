using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;

namespace MedicalCenterWinForms.Services
{
    public class FinancialReportService
    {
        private readonly DatabaseService _databaseService;

        public FinancialReportService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        // Daily Financial Summary
        public async Task<DailyFinancialSummary> GetDailyFinancialSummary(DateTime date)
        {
            using var context = _databaseService.GetDbContext();

            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            // Main Payments
            var mainPayments = await context.MainPayments
                .Include(mp => mp.PatientVisit)
                    .ThenInclude(pv => pv.Patient)
                .Include(mp => mp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(mp => mp.PaymentDate >= startDate && mp.PaymentDate < endDate)
                .ToListAsync();

            // Referral Payments
            var referralPayments = await context.ReferralPayments
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Patient)
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Include(rp => rp.MedicalService)
                .Where(rp => rp.PaymentDate >= startDate && rp.PaymentDate < endDate)
                .ToListAsync();

            var summary = new DailyFinancialSummary
            {
                Date = date,
                MainPaymentsCount = mainPayments.Count,
                ReferralPaymentsCount = referralPayments.Count,
                TotalMainPayments = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee),
                TotalReferralPayments = referralPayments.Sum(rp => rp.Amount),
                TotalDoctorShares = mainPayments.Sum(mp => mp.DoctorShare) + 
                                   referralPayments.Sum(rp => rp.DoctorShare ?? 0),
                TotalCenterShares = mainPayments.Sum(mp => mp.CenterShare) + 
                                   referralPayments.Sum(rp => rp.CenterShare),
                MainPaymentDetails = mainPayments,
                ReferralPaymentDetails = referralPayments
            };

            summary.TotalRevenue = summary.TotalMainPayments + summary.TotalReferralPayments;
            summary.TotalPayments = summary.MainPaymentsCount + summary.ReferralPaymentsCount;

            return summary;
        }

        // Monthly Financial Summary
        public async Task<MonthlyFinancialSummary> GetMonthlyFinancialSummary(int year, int month)
        {
            using var context = _databaseService.GetDbContext();

            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1);

            var dailySummaries = new List<DailyFinancialSummary>();
            var currentDate = startDate;

            while (currentDate < endDate)
            {
                var dailySummary = await GetDailyFinancialSummary(currentDate);
                dailySummaries.Add(dailySummary);
                currentDate = currentDate.AddDays(1);
            }

            var summary = new MonthlyFinancialSummary
            {
                Year = year,
                Month = month,
                DailySummaries = dailySummaries,
                TotalRevenue = dailySummaries.Sum(ds => ds.TotalRevenue),
                TotalMainPayments = dailySummaries.Sum(ds => ds.TotalMainPayments),
                TotalReferralPayments = dailySummaries.Sum(ds => ds.TotalReferralPayments),
                TotalDoctorShares = dailySummaries.Sum(ds => ds.TotalDoctorShares),
                TotalCenterShares = dailySummaries.Sum(ds => ds.TotalCenterShares),
                TotalPaymentsCount = dailySummaries.Sum(ds => ds.TotalPayments),
                WorkingDays = dailySummaries.Count(ds => ds.TotalPayments > 0)
            };

            summary.AverageDailyRevenue = summary.WorkingDays > 0 ? 
                summary.TotalRevenue / summary.WorkingDays : 0;

            return summary;
        }

        // Doctor Performance Report
        public async Task<List<DoctorPerformanceReport>> GetDoctorPerformanceReport(
            DateTime startDate, DateTime endDate)
        {
            using var context = _databaseService.GetDbContext();

            var doctors = await context.Doctors
                .Where(d => d.IsActive)
                .ToListAsync();

            var reports = new List<DoctorPerformanceReport>();

            foreach (var doctor in doctors)
            {
                // Main Payments for this doctor
                var mainPayments = await context.MainPayments
                    .Include(mp => mp.PatientVisit)
                    .Where(mp => mp.PatientVisit.DoctorId == doctor.DoctorId &&
                                mp.PaymentDate >= startDate && mp.PaymentDate <= endDate)
                    .ToListAsync();

                // Referral Payments for this doctor
                var referralPayments = await context.ReferralPayments
                    .Include(rp => rp.PatientVisit)
                    .Where(rp => rp.PatientVisit.DoctorId == doctor.DoctorId &&
                                rp.PaymentDate >= startDate && rp.PaymentDate <= endDate)
                    .ToListAsync();

                var report = new DoctorPerformanceReport
                {
                    Doctor = doctor,
                    StartDate = startDate,
                    EndDate = endDate,
                    MainPaymentsCount = mainPayments.Count,
                    ReferralPaymentsCount = referralPayments.Count,
                    TotalMainPayments = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee),
                    TotalReferralPayments = referralPayments.Sum(rp => rp.Amount),
                    TotalDoctorShares = mainPayments.Sum(mp => mp.DoctorShare) + 
                                       referralPayments.Sum(rp => rp.DoctorShare ?? 0),
                    MainPaymentDetails = mainPayments,
                    ReferralPaymentDetails = referralPayments
                };

                report.TotalRevenue = report.TotalMainPayments + report.TotalReferralPayments;
                report.TotalPatients = report.MainPaymentsCount + report.ReferralPaymentsCount;
                report.AverageRevenuePerPatient = report.TotalPatients > 0 ? 
                    report.TotalRevenue / report.TotalPatients : 0;

                reports.Add(report);
            }

            return reports.OrderByDescending(r => r.TotalRevenue).ToList();
        }

        // Service Performance Report
        public async Task<List<ServicePerformanceReport>> GetServicePerformanceReport(
            DateTime startDate, DateTime endDate)
        {
            using var context = _databaseService.GetDbContext();

            var services = await context.MedicalServices.ToListAsync();
            var reports = new List<ServicePerformanceReport>();

            foreach (var service in services)
            {
                var referralPayments = await context.ReferralPayments
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Patient)
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Doctor)
                    .Where(rp => rp.MedicalServiceId == service.MedicalServiceId &&
                                rp.PaymentDate >= startDate && rp.PaymentDate <= endDate)
                    .ToListAsync();

                var report = new ServicePerformanceReport
                {
                    Service = service,
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalPayments = referralPayments.Count,
                    TotalRevenue = referralPayments.Sum(rp => rp.Amount),
                    TotalDoctorShares = referralPayments.Sum(rp => rp.DoctorShare ?? 0),
                    TotalCenterShares = referralPayments.Sum(rp => rp.CenterShare),
                    PaymentDetails = referralPayments
                };

                report.AverageRevenuePerPayment = report.TotalPayments > 0 ? 
                    report.TotalRevenue / report.TotalPayments : 0;

                reports.Add(report);
            }

            return reports.OrderByDescending(r => r.TotalRevenue).ToList();
        }

        // Financial Trends Analysis
        public async Task<FinancialTrendsReport> GetFinancialTrendsReport(
            DateTime startDate, DateTime endDate)
        {
            var monthlyData = new List<MonthlyFinancialSummary>();
            var currentDate = new DateTime(startDate.Year, startDate.Month, 1);
            var endMonth = new DateTime(endDate.Year, endDate.Month, 1);

            while (currentDate <= endMonth)
            {
                var monthlySummary = await GetMonthlyFinancialSummary(
                    currentDate.Year, currentDate.Month);
                monthlyData.Add(monthlySummary);
                currentDate = currentDate.AddMonths(1);
            }

            var report = new FinancialTrendsReport
            {
                StartDate = startDate,
                EndDate = endDate,
                MonthlyData = monthlyData,
                TotalRevenue = monthlyData.Sum(md => md.TotalRevenue),
                TotalDoctorShares = monthlyData.Sum(md => md.TotalDoctorShares),
                TotalCenterShares = monthlyData.Sum(md => md.TotalCenterShares),
                TotalPayments = monthlyData.Sum(md => md.TotalPaymentsCount)
            };

            // Calculate growth rates
            if (monthlyData.Count > 1)
            {
                var firstMonth = monthlyData.First();
                var lastMonth = monthlyData.Last();

                if (firstMonth.TotalRevenue > 0)
                {
                    report.RevenueGrowthRate = ((lastMonth.TotalRevenue - firstMonth.TotalRevenue) / 
                                              firstMonth.TotalRevenue) * 100;
                }

                if (firstMonth.TotalPaymentsCount > 0)
                {
                    report.PaymentGrowthRate = ((lastMonth.TotalPaymentsCount - firstMonth.TotalPaymentsCount) / 
                                              (decimal)firstMonth.TotalPaymentsCount) * 100;
                }
            }

            report.AverageMonthlyRevenue = monthlyData.Count > 0 ? 
                report.TotalRevenue / monthlyData.Count : 0;

            return report;
        }

        // Top Performing Doctors
        public async Task<List<DoctorPerformanceReport>> GetTopPerformingDoctors(
            DateTime startDate, DateTime endDate, int topCount = 10)
        {
            var doctorReports = await GetDoctorPerformanceReport(startDate, endDate);
            return doctorReports.Take(topCount).ToList();
        }

        // Revenue by Payment Type
        public async Task<PaymentTypeAnalysis> GetPaymentTypeAnalysis(
            DateTime startDate, DateTime endDate)
        {
            using var context = _databaseService.GetDbContext();

            var mainPayments = await context.MainPayments
                .Where(mp => mp.PaymentDate >= startDate && mp.PaymentDate <= endDate)
                .ToListAsync();

            var referralPayments = await context.ReferralPayments
                .Where(rp => rp.PaymentDate >= startDate && rp.PaymentDate <= endDate)
                .ToListAsync();

            return new PaymentTypeAnalysis
            {
                StartDate = startDate,
                EndDate = endDate,
                MainPaymentsCount = mainPayments.Count,
                MainPaymentsRevenue = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee),
                ReferralPaymentsCount = referralPayments.Count,
                ReferralPaymentsRevenue = referralPayments.Sum(rp => rp.Amount),
                TotalRevenue = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee) + 
                              referralPayments.Sum(rp => rp.Amount),
                MainPaymentPercentage = 0, // Will be calculated after
                ReferralPaymentPercentage = 0 // Will be calculated after
            };
        }
    }
}
