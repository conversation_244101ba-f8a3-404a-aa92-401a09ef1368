using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class DashboardControl : BaseUserControl
    {

        // Dashboard Cards
        private Panel cardPatients;
        private Panel cardDoctors;
        private Panel cardAppointments;
        private Panel cardRevenue;

        // Quick Actions
        private Panel quickActionsPanel;
        private Button btnQuickAddPatient;
        private Button btnQuickAddDoctor;
        private Button btnQuickPayment;
        private Button btnQuickReport;
        private Button btnFinancialDashboard;

        // Recent Activities
        private Panel recentActivitiesPanel;
        private ListView recentActivitiesList;

        public DashboardControl(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configure main control
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(30);
            this.RightToLeft = RightToLeft.Yes;

            // Initialize dashboard components
            InitializeDashboardCards();
            InitializeQuickActions();
            InitializeRecentActivities();

            this.ResumeLayout(false);
        }

        private void InitializeDashboardCards()
        {
            // Main cards container
            var cardsContainer = new FlowLayoutPanel
            {
                Size = new Size(this.Width - 60, 200),
                Location = new Point(30, 30),
                FlowDirection = FlowDirection.RightToLeft,
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // Create dashboard cards
            this.cardPatients = CreateDashboardCard("👥", "إجمالي المراجعين", "1,234", Color.FromArgb(52, 152, 219));
            this.cardDoctors = CreateDashboardCard("👨‍⚕️", "عدد الأطباء", "45", Color.FromArgb(46, 204, 113));
            this.cardAppointments = CreateDashboardCard("📅", "المواعيد اليوم", "28", Color.FromArgb(241, 196, 15));
            this.cardRevenue = CreateDashboardCard("💰", "الإيرادات الشهرية", "125,000 ر.س", Color.FromArgb(155, 89, 182));

            cardsContainer.Controls.AddRange(new Control[] { cardPatients, cardDoctors, cardAppointments, cardRevenue });
            this.Controls.Add(cardsContainer);
        }

        private Panel CreateDashboardCard(string icon, string title, string value, Color accentColor)
        {
            var card = new Panel
            {
                Size = new Size(280, 160),
                BackColor = Color.White,
                Margin = new Padding(10),
                Padding = new Padding(25)
            };

            // Add shadow effect
            MaterialDesignHelper.AddShadow(card);

            // Icon
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 32F),
                ForeColor = accentColor,
                AutoSize = true,
                Location = new Point(25, 25)
            };

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                AutoSize = true,
                Location = new Point(25, 80)
            };

            // Value
            var valueLabel = new Label
            {
                Text = value,
                Font = ArabicFontHelper.GetArabicFont(18F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(25, 105)
            };

            // Accent line
            var accentLine = new Panel
            {
                Size = new Size(4, 160),
                BackColor = accentColor,
                Location = new Point(0, 0)
            };

            card.Controls.AddRange(new Control[] { accentLine, iconLabel, titleLabel, valueLabel });
            return card;
        }

        private void InitializeQuickActions()
        {
            // Quick Actions Panel
            this.quickActionsPanel = new Panel
            {
                Size = new Size(this.Width - 60, 120),
                Location = new Point(30, 260),
                BackColor = Color.White,
                Padding = new Padding(25),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            MaterialDesignHelper.AddShadow(quickActionsPanel);

            // Title
            var titleLabel = new Label
            {
                Text = "⚡ إجراءات سريعة",
                Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(25, 15)
            };

            // Quick action buttons
            this.btnQuickAddPatient = CreateQuickActionButton("➕ إضافة مراجع", Color.FromArgb(52, 152, 219));
            this.btnQuickAddDoctor = CreateQuickActionButton("👨‍⚕️ إضافة طبيب", Color.FromArgb(46, 204, 113));
            this.btnQuickPayment = CreateQuickActionButton("💰 تسجيل دفعة", Color.FromArgb(241, 196, 15));
            this.btnQuickReport = CreateQuickActionButton("📊 تقرير سريع", Color.FromArgb(155, 89, 182));

            var buttonsContainer = new FlowLayoutPanel
            {
                Size = new Size(this.Width - 110, 50),
                Location = new Point(25, 50),
                FlowDirection = FlowDirection.RightToLeft,
                BackColor = Color.Transparent
            };

            buttonsContainer.Controls.AddRange(new Control[] { btnQuickAddPatient, btnQuickAddDoctor, btnQuickPayment, btnQuickReport });
            quickActionsPanel.Controls.AddRange(new Control[] { titleLabel, buttonsContainer });
            this.Controls.Add(quickActionsPanel);
        }

        private Button CreateQuickActionButton(string text, Color color)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(200, 40),
                BackColor = color,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = ArabicFontHelper.GetArabicFont(11F),
                Margin = new Padding(5),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            return button;
        }

        private void InitializeRecentActivities()
        {
            // Recent Activities Panel
            this.recentActivitiesPanel = new Panel
            {
                Size = new Size(this.Width - 60, 300),
                Location = new Point(30, 400),
                BackColor = Color.White,
                Padding = new Padding(25),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            MaterialDesignHelper.AddShadow(recentActivitiesPanel);

            // Title
            var titleLabel = new Label
            {
                Text = "📋 الأنشطة الأخيرة",
                Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(25, 15)
            };

            // Recent activities list
            this.recentActivitiesList = new ListView
            {
                Size = new Size(this.Width - 110, 230),
                Location = new Point(25, 50),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = ArabicFontHelper.GetArabicFont(10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // Add columns
            recentActivitiesList.Columns.Add("الوقت", 120);
            recentActivitiesList.Columns.Add("النشاط", 200);
            recentActivitiesList.Columns.Add("المستخدم", 150);
            recentActivitiesList.Columns.Add("التفاصيل", 300);

            recentActivitiesPanel.Controls.AddRange(new Control[] { titleLabel, recentActivitiesList });
            this.Controls.Add(recentActivitiesPanel);
        }

        private void LoadDashboardData()
        {
            // Load recent activities
            LoadRecentActivities();
        }

        private void LoadRecentActivities()
        {
            // Sample data - replace with actual database queries
            var activities = new[]
            {
                new { Time = "14:30", Activity = "إضافة مراجع جديد", User = "أحمد محمد", Details = "تم إضافة المراجع: محمد علي أحمد" },
                new { Time = "14:15", Activity = "تسجيل دفعة", User = "فاطمة سالم", Details = "دفعة بقيمة 500 ر.س للمراجع رقم 1234" },
                new { Time = "13:45", Activity = "إضافة طبيب", User = "المدير العام", Details = "تم إضافة الطبيب: د. سارة أحمد - تخصص أطفال" },
                new { Time = "13:20", Activity = "تعديل موعد", User = "ريم خالد", Details = "تم تعديل موعد المراجع رقم 5678" },
                new { Time = "12:55", Activity = "إنشاء تقرير", User = "المدير العام", Details = "تقرير المراجعين الشهري - يناير 2025" }
            };

            foreach (var activity in activities)
            {
                var item = new ListViewItem(activity.Time);
                item.SubItems.Add(activity.Activity);
                item.SubItems.Add(activity.User);
                item.SubItems.Add(activity.Details);
                recentActivitiesList.Items.Add(item);
            }
        }
    }
}
