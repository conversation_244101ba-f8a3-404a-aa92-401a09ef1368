using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;

namespace MedicalCenterWinForms.Services
{
    public class PerformanceTrackingService
    {
        private readonly DatabaseService _databaseService;

        public PerformanceTrackingService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        // Performance Metrics
        public async Task<PerformanceMetrics> GetPerformanceMetrics(DateTime startDate, DateTime endDate)
        {
            using var context = _databaseService.GetDbContext();

            var mainPayments = await context.MainPayments
                .Include(mp => mp.PatientVisit)
                    .ThenInclude(pv => pv.Patient)
                .Include(mp => mp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(mp => mp.PaymentDate >= startDate && mp.PaymentDate <= endDate)
                .ToListAsync();

            var referralPayments = await context.ReferralPayments
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Patient)
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Include(rp => rp.MedicalService)
                .Where(rp => rp.PaymentDate >= startDate && rp.PaymentDate <= endDate)
                .ToListAsync();

            var totalRevenue = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee) + 
                              referralPayments.Sum(rp => rp.Amount);

            var totalDoctorShares = mainPayments.Sum(mp => mp.DoctorShare) + 
                                   referralPayments.Sum(rp => rp.DoctorShare ?? 0);

            var totalCenterShares = mainPayments.Sum(mp => mp.CenterShare) + 
                                   referralPayments.Sum(rp => rp.CenterShare);

            var totalPayments = mainPayments.Count + referralPayments.Count;

            var uniquePatients = mainPayments.Select(mp => mp.PatientVisit.PatientId)
                .Union(referralPayments.Select(rp => rp.PatientVisit.PatientId))
                .Distinct().Count();

            var activeDoctors = mainPayments.Select(mp => mp.PatientVisit.DoctorId)
                .Union(referralPayments.Select(rp => rp.PatientVisit.DoctorId))
                .Distinct().Count();

            return new PerformanceMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRevenue = totalRevenue,
                TotalDoctorShares = totalDoctorShares,
                TotalCenterShares = totalCenterShares,
                TotalPayments = totalPayments,
                UniquePatients = uniquePatients,
                ActiveDoctors = activeDoctors,
                AverageTransactionValue = totalPayments > 0 ? totalRevenue / totalPayments : 0,
                RevenuePerPatient = uniquePatients > 0 ? totalRevenue / uniquePatients : 0,
                RevenuePerDoctor = activeDoctors > 0 ? totalRevenue / activeDoctors : 0,
                DoctorSharePercentage = totalRevenue > 0 ? (totalDoctorShares / totalRevenue) * 100 : 0,
                CenterSharePercentage = totalRevenue > 0 ? (totalCenterShares / totalRevenue) * 100 : 0
            };
        }

        // Daily Performance Tracking
        public async Task<List<DailyPerformance>> GetDailyPerformance(DateTime startDate, DateTime endDate)
        {
            var dailyPerformances = new List<DailyPerformance>();
            var currentDate = startDate.Date;

            while (currentDate <= endDate.Date)
            {
                var metrics = await GetPerformanceMetrics(currentDate, currentDate.AddDays(1).AddTicks(-1));
                
                dailyPerformances.Add(new DailyPerformance
                {
                    Date = currentDate,
                    Revenue = metrics.TotalRevenue,
                    Payments = metrics.TotalPayments,
                    Patients = metrics.UniquePatients,
                    DoctorShares = metrics.TotalDoctorShares,
                    CenterShares = metrics.TotalCenterShares,
                    AverageTransaction = metrics.AverageTransactionValue
                });

                currentDate = currentDate.AddDays(1);
            }

            return dailyPerformances;
        }

        // Doctor Performance Comparison
        public async Task<List<DoctorPerformanceComparison>> GetDoctorPerformanceComparison(
            DateTime startDate, DateTime endDate)
        {
            using var context = _databaseService.GetDbContext();

            var doctors = await context.Doctors
                .Where(d => d.IsActive)
                .ToListAsync();

            var comparisons = new List<DoctorPerformanceComparison>();

            foreach (var doctor in doctors)
            {
                var mainPayments = await context.MainPayments
                    .Include(mp => mp.PatientVisit)
                    .Where(mp => mp.PatientVisit.DoctorId == doctor.DoctorId &&
                                mp.PaymentDate >= startDate && mp.PaymentDate <= endDate)
                    .ToListAsync();

                var referralPayments = await context.ReferralPayments
                    .Include(rp => rp.PatientVisit)
                    .Where(rp => rp.PatientVisit.DoctorId == doctor.DoctorId &&
                                rp.PaymentDate >= startDate && rp.PaymentDate <= endDate)
                    .ToListAsync();

                var totalRevenue = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee) + 
                                  referralPayments.Sum(rp => rp.Amount);

                var totalShares = mainPayments.Sum(mp => mp.DoctorShare) + 
                                 referralPayments.Sum(rp => rp.DoctorShare ?? 0);

                var totalPatients = mainPayments.Select(mp => mp.PatientVisit.PatientId)
                    .Union(referralPayments.Select(rp => rp.PatientVisit.PatientId))
                    .Distinct().Count();

                comparisons.Add(new DoctorPerformanceComparison
                {
                    Doctor = doctor,
                    TotalRevenue = totalRevenue,
                    TotalShares = totalShares,
                    TotalPatients = totalPatients,
                    MainPayments = mainPayments.Count,
                    ReferralPayments = referralPayments.Count,
                    AverageRevenuePerPatient = totalPatients > 0 ? totalRevenue / totalPatients : 0,
                    SharePercentage = totalRevenue > 0 ? (totalShares / totalRevenue) * 100 : 0
                });
            }

            return comparisons.OrderByDescending(c => c.TotalRevenue).ToList();
        }

        // Service Performance Analysis
        public async Task<List<ServicePerformanceAnalysis>> GetServicePerformanceAnalysis(
            DateTime startDate, DateTime endDate)
        {
            using var context = _databaseService.GetDbContext();

            var services = await context.MedicalServices.ToListAsync();
            var analyses = new List<ServicePerformanceAnalysis>();

            foreach (var service in services)
            {
                var referralPayments = await context.ReferralPayments
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Patient)
                    .Include(rp => rp.PatientVisit)
                        .ThenInclude(pv => pv.Doctor)
                    .Where(rp => rp.MedicalServiceId == service.MedicalServiceId &&
                                rp.PaymentDate >= startDate && rp.PaymentDate <= endDate)
                    .ToListAsync();

                var totalRevenue = referralPayments.Sum(rp => rp.Amount);
                var totalUsage = referralPayments.Count;
                var uniquePatients = referralPayments.Select(rp => rp.PatientVisit.PatientId).Distinct().Count();
                var uniqueDoctors = referralPayments.Select(rp => rp.PatientVisit.DoctorId).Distinct().Count();

                analyses.Add(new ServicePerformanceAnalysis
                {
                    Service = service,
                    TotalRevenue = totalRevenue,
                    TotalUsage = totalUsage,
                    UniquePatients = uniquePatients,
                    UniqueDoctors = uniqueDoctors,
                    AveragePrice = totalUsage > 0 ? totalRevenue / totalUsage : 0,
                    RevenuePerPatient = uniquePatients > 0 ? totalRevenue / uniquePatients : 0,
                    UsagePerDoctor = uniqueDoctors > 0 ? (decimal)totalUsage / uniqueDoctors : 0
                });
            }

            return analyses.OrderByDescending(a => a.TotalRevenue).ToList();
        }

        // Revenue Forecasting
        public async Task<RevenueForecast> GetRevenueForecast(DateTime forecastDate, int daysToForecast = 30)
        {
            var historicalDays = 90; // Use last 90 days for forecasting
            var startDate = forecastDate.AddDays(-historicalDays);
            
            var dailyPerformances = await GetDailyPerformance(startDate, forecastDate.AddDays(-1));
            
            if (!dailyPerformances.Any())
            {
                return new RevenueForecast
                {
                    ForecastDate = forecastDate,
                    DaysToForecast = daysToForecast,
                    PredictedRevenue = 0,
                    ConfidenceLevel = 0,
                    TrendDirection = "Unknown"
                };
            }

            // Simple linear regression for forecasting
            var averageDailyRevenue = dailyPerformances.Average(dp => dp.Revenue);
            var recentTrend = CalculateTrend(dailyPerformances.TakeLast(30).ToList());
            
            var predictedDailyRevenue = averageDailyRevenue + (recentTrend * daysToForecast / 2);
            var predictedRevenue = predictedDailyRevenue * daysToForecast;

            return new RevenueForecast
            {
                ForecastDate = forecastDate,
                DaysToForecast = daysToForecast,
                PredictedRevenue = Math.Max(0, predictedRevenue),
                ConfidenceLevel = CalculateConfidenceLevel(dailyPerformances),
                TrendDirection = recentTrend > 0 ? "Increasing" : recentTrend < 0 ? "Decreasing" : "Stable",
                HistoricalAverage = averageDailyRevenue,
                TrendValue = recentTrend
            };
        }

        // Performance Alerts
        public async Task<List<PerformanceAlert>> GetPerformanceAlerts(DateTime checkDate)
        {
            var alerts = new List<PerformanceAlert>();
            
            var todayMetrics = await GetPerformanceMetrics(checkDate, checkDate.AddDays(1).AddTicks(-1));
            var yesterdayMetrics = await GetPerformanceMetrics(checkDate.AddDays(-1), checkDate.AddTicks(-1));
            var lastWeekMetrics = await GetPerformanceMetrics(checkDate.AddDays(-7), checkDate.AddDays(-6).AddTicks(-1));

            // Revenue drop alert
            if (todayMetrics.TotalRevenue < yesterdayMetrics.TotalRevenue * 0.7m)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "Revenue Drop",
                    Severity = "High",
                    Message = $"انخفاض الإيرادات بنسبة {((yesterdayMetrics.TotalRevenue - todayMetrics.TotalRevenue) / yesterdayMetrics.TotalRevenue * 100):F1}%",
                    Date = checkDate,
                    Value = todayMetrics.TotalRevenue,
                    PreviousValue = yesterdayMetrics.TotalRevenue
                });
            }

            // Low payment count alert
            if (todayMetrics.TotalPayments < 5)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "Low Activity",
                    Severity = todayMetrics.TotalPayments == 0 ? "Critical" : "Medium",
                    Message = $"عدد قليل من المدفوعات: {todayMetrics.TotalPayments}",
                    Date = checkDate,
                    Value = todayMetrics.TotalPayments,
                    PreviousValue = yesterdayMetrics.TotalPayments
                });
            }

            // Doctor share percentage alert
            if (todayMetrics.DoctorSharePercentage > 70)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "High Doctor Share",
                    Severity = "Medium",
                    Message = $"نسبة حصص الأطباء مرتفعة: {todayMetrics.DoctorSharePercentage:F1}%",
                    Date = checkDate,
                    Value = todayMetrics.DoctorSharePercentage,
                    PreviousValue = yesterdayMetrics.DoctorSharePercentage
                });
            }

            return alerts;
        }

        // Helper Methods
        private decimal CalculateTrend(List<DailyPerformance> performances)
        {
            if (performances.Count < 2) return 0;

            var firstHalf = performances.Take(performances.Count / 2).Average(p => p.Revenue);
            var secondHalf = performances.Skip(performances.Count / 2).Average(p => p.Revenue);

            return secondHalf - firstHalf;
        }

        private decimal CalculateConfidenceLevel(List<DailyPerformance> performances)
        {
            if (performances.Count < 7) return 50; // Low confidence with limited data

            var revenues = performances.Select(p => p.Revenue).ToList();
            var average = revenues.Average();
            var variance = revenues.Sum(r => (r - average) * (r - average)) / revenues.Count;
            var standardDeviation = (decimal)Math.Sqrt((double)variance);

            // Lower standard deviation = higher confidence
            var coefficientOfVariation = average > 0 ? standardDeviation / average : 1;
            var confidence = Math.Max(30, Math.Min(95, 100 - (coefficientOfVariation * 100)));

            return confidence;
        }
    }
}
