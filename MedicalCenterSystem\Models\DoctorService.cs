using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterSystem.Models
{
    public class DoctorService
    {
        public int DoctorServiceId { get; set; }

        [Required]
        public int DoctorId { get; set; }

        [Required]
        public int MedicalServiceId { get; set; }

        [StringLength(20)]
        [Display(Name = "نوع الربط")]
        public string LinkType { get; set; } = string.Empty; // Direct / Referral

        [Display(Name = "له نسبة")]
        public bool HasPercentage { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "النسبة")]
        public decimal? Percentage { get; set; }

        // Navigation Properties
        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        [ForeignKey("MedicalServiceId")]
        public virtual MedicalService MedicalService { get; set; } = null!;
    }
}
