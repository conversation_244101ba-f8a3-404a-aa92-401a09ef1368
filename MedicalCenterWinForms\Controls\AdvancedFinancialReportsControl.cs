using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class AdvancedFinancialReportsControl : BaseUserControl
    {
        private FinancialReportService _reportService;
        private ReportExportService _exportService;

        // UI Components
        private Panel mainPanel;
        private TabControl tabReports;
        private TabPage tabDashboard;
        private TabPage tabDailyReports;
        private TabPage tabMonthlyReports;
        private TabPage tabDoctorPerformance;
        private TabPage tabServiceAnalysis;
        private TabPage tabTrends;

        // Dashboard Components
        private Panel dashboardPanel;
        private Panel summaryCardsPanel;
        private Panel chartPlaceholder;
        private DataGridView dgvTopDoctors;
        private DataGridView dgvTopServices;

        // Report Controls
        private DateTimePicker dtpStartDate;
        private DateTimePicker dtpEndDate;
        private Button btnGenerateReport;
        private Button btnExportReport;
        private Button btnPrintReport;
        private ComboBox cmbReportType;

        // Summary Cards
        private Panel cardTodayRevenue;
        private Panel cardWeekRevenue;
        private Panel cardMonthRevenue;
        private Panel cardYearRevenue;

        public AdvancedFinancialReportsControl() : base()
        {
            InitializeComponent();
            InitializeReportService();
            LoadDashboard();
        }

        public AdvancedFinancialReportsControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializeReportService();
            LoadDashboard();
        }

        private void InitializeReportService()
        {
            _reportService = new FinancialReportService(DatabaseService);
            _exportService = new ReportExportService();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateTabControl();
            CreateDashboardTab();
            CreateReportTabs();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                "التقارير المالية المتقدمة", true);
            mainPanel.Size = new Size(1180, 780);
            mainPanel.Location = new Point(10, 10);
            this.Controls.Add(mainPanel);
        }

        private void CreateTabControl()
        {
            tabReports = new TabControl
            {
                Location = new Point(20, 60),
                Size = new Size(1140, 700),
                Font = ArabicFontHelper.GetArabicFont(10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            // Create tabs
            tabDashboard = new TabPage("لوحة المعلومات المالية");
            tabDailyReports = new TabPage("التقارير اليومية");
            tabMonthlyReports = new TabPage("التقارير الشهرية");
            tabDoctorPerformance = new TabPage("أداء الأطباء");
            tabServiceAnalysis = new TabPage("تحليل الخدمات");
            tabTrends = new TabPage("الاتجاهات المالية");

            tabReports.TabPages.AddRange(new TabPage[]
            {
                tabDashboard, tabDailyReports, tabMonthlyReports,
                tabDoctorPerformance, tabServiceAnalysis, tabTrends
            });

            mainPanel.Controls.Add(tabReports);
        }

        private void CreateDashboardTab()
        {
            dashboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(10)
            };

            CreateSummaryCards();
            CreateChartPlaceholder();
            CreateTopPerformersGrids();

            tabDashboard.Controls.Add(dashboardPanel);
        }

        private void CreateSummaryCards()
        {
            summaryCardsPanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(1100, 120),
                BackColor = Color.Transparent
            };

            // Today Revenue Card
            cardTodayRevenue = CreateSummaryCard("إيرادات اليوم", "0.00 ريال", 
                MaterialDesignHelper.Colors.Success, new Point(10, 10));

            // Week Revenue Card
            cardWeekRevenue = CreateSummaryCard("إيرادات الأسبوع", "0.00 ريال", 
                MaterialDesignHelper.Colors.Primary, new Point(280, 10));

            // Month Revenue Card
            cardMonthRevenue = CreateSummaryCard("إيرادات الشهر", "0.00 ريال", 
                MaterialDesignHelper.Colors.Info, new Point(550, 10));

            // Year Revenue Card
            cardYearRevenue = CreateSummaryCard("إيرادات السنة", "0.00 ريال", 
                MaterialDesignHelper.Colors.Warning, new Point(820, 10));

            summaryCardsPanel.Controls.AddRange(new Control[]
            {
                cardTodayRevenue, cardWeekRevenue, cardMonthRevenue, cardYearRevenue
            });

            dashboardPanel.Controls.Add(summaryCardsPanel);
        }

        private Panel CreateSummaryCard(string title, string value, Color color, Point location)
        {
            var card = new Panel
            {
                Location = location,
                Size = new Size(250, 100),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            MaterialDesignHelper.AddShadow(card);

            var lblTitle = new Label
            {
                Text = title,
                Location = new Point(10, 10),
                Size = new Size(230, 25),
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.TopCenter
            };

            var lblValue = new Label
            {
                Text = value,
                Location = new Point(10, 40),
                Size = new Size(230, 40),
                Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter
            };

            card.Controls.AddRange(new Control[] { lblTitle, lblValue });
            return card;
        }

        private void CreateChartPlaceholder()
        {
            chartPlaceholder = new Panel
            {
                Location = new Point(10, 140),
                Size = new Size(1100, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblChartInfo = new Label
            {
                Text = "الرسوم البيانية ستكون متاحة في الإصدار القادم",
                Location = new Point(10, 130),
                Size = new Size(1080, 40),
                Font = ArabicFontHelper.GetArabicFont(14F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.MiddleCenter
            };

            chartPlaceholder.Controls.Add(lblChartInfo);
            dashboardPanel.Controls.Add(chartPlaceholder);
        }

        private void CreateTopPerformersGrids()
        {
            // Top Doctors Grid
            dgvTopDoctors = new DataGridView
            {
                Location = new Point(10, 460),
                Size = new Size(540, 200),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            dgvTopDoctors.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "DoctorName", HeaderText = "اسم الطبيب", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Revenue", HeaderText = "الإيرادات", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Patients", HeaderText = "عدد المراجعين", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Shares", HeaderText = "الحصص", Width = 120 }
            });

            // Top Services Grid
            dgvTopServices = new DataGridView
            {
                Location = new Point(570, 460),
                Size = new Size(540, 200),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            dgvTopServices.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "ServiceName", HeaderText = "اسم الخدمة", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Revenue", HeaderText = "الإيرادات", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Usage", HeaderText = "عدد الاستخدام", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Average", HeaderText = "المتوسط", Width = 120 }
            });

            dashboardPanel.Controls.AddRange(new Control[] { dgvTopDoctors, dgvTopServices });
        }

        private void CreateReportTabs()
        {
            CreateDailyReportsTab();
            CreateSimpleReportsTab();
        }

        private void CreateDailyReportsTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Date Selection
            var lblDate = new Label
            {
                Text = "اختر التاريخ:",
                Location = new Point(10, 10),
                Size = new Size(100, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var dtpDate = new DateTimePicker
            {
                Location = new Point(120, 10),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            var btnGenerateDaily = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔍 إنشاء التقرير", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnGenerateDaily.Location = new Point(280, 10);
            btnGenerateDaily.Size = new Size(120, 30);

            var btnExportDaily = ModernMedicalTheme.Components.CreateAdvancedButton(
                "📤 تصدير", ModernMedicalTheme.Components.ButtonStyle.Success);
            btnExportDaily.Location = new Point(410, 10);
            btnExportDaily.Size = new Size(80, 30);

            // Results Panel
            var resultsPanel = new Panel
            {
                Location = new Point(10, 50),
                Size = new Size(1100, 600),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var dgvDailyReport = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            dgvDailyReport.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "PaymentId", HeaderText = "رقم المدفوعة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "PatientName", HeaderText = "اسم المراجع", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "DoctorName", HeaderText = "اسم الطبيب", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ServiceType", HeaderText = "نوع الخدمة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Amount", HeaderText = "المبلغ", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "DoctorShare", HeaderText = "حصة الطبيب", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CenterShare", HeaderText = "حصة المركز", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "PaymentTime", HeaderText = "وقت الدفع", Width = 120 }
            });

            resultsPanel.Controls.Add(dgvDailyReport);

            panel.Controls.AddRange(new Control[] { lblDate, dtpDate, btnGenerateDaily, btnExportDaily, resultsPanel });
            tabDailyReports.Controls.Add(panel);

            // Event handlers
            btnGenerateDaily.Click += async (s, e) => await GenerateDailyReport(dtpDate.Value, dgvDailyReport);
            btnExportDaily.Click += async (s, e) => await ExportDailyReport(dtpDate.Value);
        }

        private void CreateSimpleReportsTab()
        {
            var simpleTab = new TabPage("التقارير البسيطة");
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var lblInfo = new Label
            {
                Text = "التقارير المتقدمة مع الرسوم البيانية ستكون متاحة في الإصدار القادم",
                Location = new Point(20, 50),
                Size = new Size(800, 40),
                Font = ArabicFontHelper.GetArabicFont(14F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                TextAlign = ContentAlignment.MiddleCenter
            };

            var btnBasicReports = ModernMedicalTheme.Components.CreateAdvancedButton(
                "📊 التقارير الأساسية", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnBasicReports.Location = new Point(350, 120);
            btnBasicReports.Size = new Size(150, 40);
            btnBasicReports.Click += (s, e) => ShowInfo("يمكنك الوصول للتقارير الأساسية من القائمة الرئيسية");

            panel.Controls.AddRange(new Control[] { lblInfo, btnBasicReports });
            simpleTab.Controls.Add(panel);
            mainTabControl.TabPages.Add(simpleTab);
        }
    }
}

            monthlyChart.ChartAreas.Add(chartArea);

            var series = new Series("الإيرادات اليومية")
            {
                ChartType = SeriesChartType.Column,
                Color = MaterialDesignHelper.Colors.Primary
            };
            monthlyChart.Series.Add(series);

            chartsPanel.Controls.Add(monthlyChart);

            // Summary Panel
            var summaryPanel = new Panel
            {
                Location = new Point(10, 360),
                Size = new Size(1100, 290),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var dgvMonthlySummary = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            dgvMonthlySummary.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Date", HeaderText = "التاريخ", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "MainPayments", HeaderText = "المدفوعات الرئيسية", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ReferralPayments", HeaderText = "مدفوعات التحويل", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "TotalRevenue", HeaderText = "إجمالي الإيرادات", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "DoctorShares", HeaderText = "حصص الأطباء", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "CenterShares", HeaderText = "حصص المركز", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "PaymentsCount", HeaderText = "عدد المدفوعات", Width = 100 }
            });

            summaryPanel.Controls.Add(dgvMonthlySummary);

            panel.Controls.AddRange(new Control[]
            {
                lblMonth, cmbMonth, lblYear, nudYear, btnGenerateMonthly,
                chartsPanel, summaryPanel
            });
            tabMonthlyReports.Controls.Add(panel);

            // Event handler
            btnGenerateMonthly.Click += async (s, e) =>
                await GenerateMonthlyReport((int)nudYear.Value, cmbMonth.SelectedIndex + 1,
                                          monthlyChart, dgvMonthlySummary);
        }

        private void CreateDoctorPerformanceTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Date Range Selection
            CreateDateRangeControls(panel, out dtpStartDate, out dtpEndDate, out btnGenerateReport);

            // Performance Chart
            var performanceChart = new Chart
            {
                Location = new Point(10, 50),
                Size = new Size(1100, 300),
                BackColor = Color.White
            };

            var chartArea = new ChartArea("PerformanceArea");
            chartArea.AxisX.Title = "الأطباء";
            chartArea.AxisY.Title = "الإيرادات (ريال)";
            performanceChart.ChartAreas.Add(chartArea);

            var revenueSeries = new Series("الإيرادات")
            {
                ChartType = SeriesChartType.Column,
                Color = MaterialDesignHelper.Colors.Success
            };
            performanceChart.Series.Add(revenueSeries);

            // Performance Grid
            var dgvDoctorPerformance = new DataGridView
            {
                Location = new Point(10, 360),
                Size = new Size(1100, 290),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            dgvDoctorPerformance.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "DoctorName", HeaderText = "اسم الطبيب", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Specialty", HeaderText = "التخصص", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "TotalPatients", HeaderText = "عدد المراجعين", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalRevenue", HeaderText = "إجمالي الإيرادات", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "DoctorShares", HeaderText = "حصص الطبيب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "AveragePerPatient", HeaderText = "متوسط المراجع", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "MainPayments", HeaderText = "المدفوعات الرئيسية", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ReferralPayments", HeaderText = "مدفوعات التحويل", Width = 120 }
            });

            panel.Controls.AddRange(new Control[] { performanceChart, dgvDoctorPerformance });
            tabDoctorPerformance.Controls.Add(panel);

            // Event handler
            btnGenerateReport.Click += async (s, e) =>
                await GenerateDoctorPerformanceReport(dtpStartDate.Value, dtpEndDate.Value,
                                                    performanceChart, dgvDoctorPerformance);
        }

        private void CreateServiceAnalysisTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Date Range Selection
            DateTimePicker dtpServiceStart, dtpServiceEnd;
            Button btnGenerateService;
            CreateDateRangeControls(panel, out dtpServiceStart, out dtpServiceEnd, out btnGenerateService);

            // Service Performance Chart
            var serviceChart = new Chart
            {
                Location = new Point(10, 50),
                Size = new Size(550, 300),
                BackColor = Color.White
            };

            var chartArea = new ChartArea("ServiceArea");
            chartArea.AxisX.Title = "الخدمات";
            chartArea.AxisY.Title = "الإيرادات (ريال)";
            serviceChart.ChartAreas.Add(chartArea);

            var serviceSeries = new Series("إيرادات الخدمات")
            {
                ChartType = SeriesChartType.Pie,
                IsValueShownAsLabel = true
            };
            serviceChart.Series.Add(serviceSeries);

            // Service Usage Chart
            var usageChart = new Chart
            {
                Location = new Point(570, 50),
                Size = new Size(550, 300),
                BackColor = Color.White
            };

            var usageChartArea = new ChartArea("UsageArea");
            usageChartArea.AxisX.Title = "الخدمات";
            usageChartArea.AxisY.Title = "عدد الاستخدام";
            usageChart.ChartAreas.Add(usageChartArea);

            var usageSeries = new Series("استخدام الخدمات")
            {
                ChartType = SeriesChartType.Column,
                Color = MaterialDesignHelper.Colors.Info
            };
            usageChart.Series.Add(usageSeries);

            // Service Analysis Grid
            var dgvServiceAnalysis = new DataGridView
            {
                Location = new Point(10, 360),
                Size = new Size(1100, 290),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes
            };

            dgvServiceAnalysis.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "ServiceName", HeaderText = "اسم الخدمة", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ServiceType", HeaderText = "نوع الخدمة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalUsage", HeaderText = "عدد الاستخدام", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalRevenue", HeaderText = "إجمالي الإيرادات", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "AveragePrice", HeaderText = "متوسط السعر", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "DoctorShares", HeaderText = "حصص الأطباء", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "CenterShares", HeaderText = "حصص المركز", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "RevenuePercentage", HeaderText = "نسبة الإيرادات %", Width = 100 }
            });

            panel.Controls.AddRange(new Control[] { serviceChart, usageChart, dgvServiceAnalysis });
            tabServiceAnalysis.Controls.Add(panel);

            // Event handler
            btnGenerateService.Click += async (s, e) =>
                await GenerateServiceAnalysisReport(dtpServiceStart.Value, dtpServiceEnd.Value,
                                                   serviceChart, usageChart, dgvServiceAnalysis);
        }

        private void CreateTrendsTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Date Range Selection
            DateTimePicker dtpTrendsStart, dtpTrendsEnd;
            Button btnGenerateTrends;
            CreateDateRangeControls(panel, out dtpTrendsStart, out dtpTrendsEnd, out btnGenerateTrends);

            // Trends Chart
            var trendsChart = new Chart
            {
                Location = new Point(10, 50),
                Size = new Size(1100, 400),
                BackColor = Color.White
            };

            var chartArea = new ChartArea("TrendsArea");
            chartArea.AxisX.Title = "الشهر";
            chartArea.AxisY.Title = "المبلغ (ريال)";
            trendsChart.ChartAreas.Add(chartArea);

            var revenueTrendSeries = new Series("اتجاه الإيرادات")
            {
                ChartType = SeriesChartType.Line,
                Color = MaterialDesignHelper.Colors.Success,
                BorderWidth = 3
            };

            var doctorSharesTrendSeries = new Series("اتجاه حصص الأطباء")
            {
                ChartType = SeriesChartType.Line,
                Color = MaterialDesignHelper.Colors.Primary,
                BorderWidth = 3
            };

            trendsChart.Series.Add(revenueTrendSeries);
            trendsChart.Series.Add(doctorSharesTrendSeries);

            // Trends Summary
            var summaryPanel = new Panel
            {
                Location = new Point(10, 460),
                Size = new Size(1100, 190),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblTrendsTitle = new Label
            {
                Text = "ملخص الاتجاهات المالية",
                Location = new Point(10, 10),
                Size = new Size(200, 25),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary
            };

            var lblRevenueGrowth = new Label
            {
                Text = "معدل نمو الإيرادات: 0%",
                Location = new Point(10, 40),
                Size = new Size(250, 25),
                Font = ArabicFontHelper.GetArabicFont(10F)
            };

            var lblPaymentGrowth = new Label
            {
                Text = "معدل نمو المدفوعات: 0%",
                Location = new Point(10, 70),
                Size = new Size(250, 25),
                Font = ArabicFontHelper.GetArabicFont(10F)
            };

            var lblAverageMonthly = new Label
            {
                Text = "متوسط الإيرادات الشهرية: 0 ريال",
                Location = new Point(10, 100),
                Size = new Size(250, 25),
                Font = ArabicFontHelper.GetArabicFont(10F)
            };

            var lblTotalRevenue = new Label
            {
                Text = "إجمالي الإيرادات: 0 ريال",
                Location = new Point(10, 130),
                Size = new Size(250, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Success
            };

            summaryPanel.Controls.AddRange(new Control[]
            {
                lblTrendsTitle, lblRevenueGrowth, lblPaymentGrowth,
                lblAverageMonthly, lblTotalRevenue
            });

            panel.Controls.AddRange(new Control[] { trendsChart, summaryPanel });
            tabTrends.Controls.Add(panel);

            // Event handler
            btnGenerateTrends.Click += async (s, e) =>
                await GenerateTrendsReport(dtpTrendsStart.Value, dtpTrendsEnd.Value,
                                         trendsChart, lblRevenueGrowth, lblPaymentGrowth,
                                         lblAverageMonthly, lblTotalRevenue);
        }

        private void CreateDateRangeControls(Panel parent, out DateTimePicker startDate,
                                           out DateTimePicker endDate, out Button generateButton)
        {
            var lblStartDate = new Label
            {
                Text = "من تاريخ:",
                Location = new Point(10, 10),
                Size = new Size(60, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            startDate = new DateTimePicker
            {
                Location = new Point(80, 10),
                Size = new Size(120, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddDays(-30)
            };

            var lblEndDate = new Label
            {
                Text = "إلى تاريخ:",
                Location = new Point(210, 10),
                Size = new Size(60, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            endDate = new DateTimePicker
            {
                Location = new Point(280, 10),
                Size = new Size(120, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            generateButton = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔍 إنشاء التقرير", ModernMedicalTheme.Components.ButtonStyle.Primary);
            generateButton.Location = new Point(410, 10);
            generateButton.Size = new Size(120, 30);

            parent.Controls.AddRange(new Control[]
            {
                lblStartDate, startDate, lblEndDate, endDate, generateButton
            });
        }

        // Report Generation Methods
        private async void LoadDashboard()
        {
            try
            {
                SetLoadingState(true);

                var today = DateTime.Today;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(today.Year, today.Month, 1);
                var yearStart = new DateTime(today.Year, 1, 1);

                // Load dashboard data
                var todaySummary = await _reportService.GetDailyFinancialSummary(today);
                var weekSummary = await _reportService.GetFinancialTrendsReport(weekStart, today);
                var monthSummary = await _reportService.GetMonthlyFinancialSummary(today.Year, today.Month);
                var yearSummary = await _reportService.GetFinancialTrendsReport(yearStart, today);

                // Update summary cards
                UpdateSummaryCard(cardTodayRevenue, "إيرادات اليوم", todaySummary.TotalRevenue);
                UpdateSummaryCard(cardWeekRevenue, "إيرادات الأسبوع", weekSummary.TotalRevenue);
                UpdateSummaryCard(cardMonthRevenue, "إيرادات الشهر", monthSummary.TotalRevenue);
                UpdateSummaryCard(cardYearRevenue, "إيرادات السنة", yearSummary.TotalRevenue);

                // Charts will be loaded in future version

                // Load top performers
                await LoadTopPerformers();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل لوحة المعلومات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private void UpdateSummaryCard(Panel card, string title, decimal value)
        {
            var lblValue = card.Controls.OfType<Label>().FirstOrDefault(l => l.Font.Bold);
            if (lblValue != null)
            {
                lblValue.Text = value.ToString("C");
            }
        }



        private async Task LoadTopPerformers()
        {
            try
            {
                var startDate = DateTime.Today.AddDays(-30);
                var endDate = DateTime.Today;

                // Load top doctors
                var topDoctors = await _reportService.GetTopPerformingDoctors(startDate, endDate, 5);
                dgvTopDoctors.Rows.Clear();

                foreach (var doctor in topDoctors)
                {
                    dgvTopDoctors.Rows.Add(
                        doctor.Doctor.FullName,
                        doctor.TotalRevenue.ToString("C"),
                        doctor.TotalPatients,
                        doctor.TotalDoctorShares.ToString("C")
                    );
                }

                // Load top services
                var topServices = await _reportService.GetServicePerformanceReport(startDate, endDate);
                dgvTopServices.Rows.Clear();

                foreach (var service in topServices.Take(5))
                {
                    dgvTopServices.Rows.Add(
                        service.Service.ServiceName,
                        service.TotalRevenue.ToString("C"),
                        service.TotalPayments,
                        service.AverageRevenuePerPayment.ToString("C")
                    );
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل أفضل الأداءات: {ex.Message}");
            }
        }

        private async Task GenerateDailyReport(DateTime date, DataGridView grid)
        {
            try
            {
                SetLoadingState(true);
                grid.Rows.Clear();

                var dailySummary = await _reportService.GetDailyFinancialSummary(date);

                // Add main payments
                foreach (var payment in dailySummary.MainPaymentDetails)
                {
                    grid.Rows.Add(
                        $"MP{payment.MainPaymentId:D4}",
                        payment.PatientVisit.Patient.PatientName,
                        payment.PatientVisit.Doctor.FullName,
                        "مدفوعة رئيسية",
                        (payment.ConsultationFee + payment.ExamFee).ToString("C"),
                        payment.DoctorShare.ToString("C"),
                        payment.CenterShare.ToString("C"),
                        payment.PaymentDate.ToString("HH:mm")
                    );
                }

                // Add referral payments
                foreach (var payment in dailySummary.ReferralPaymentDetails)
                {
                    grid.Rows.Add(
                        $"RP{payment.ReferralPaymentId:D4}",
                        payment.PatientVisit.Patient.PatientName,
                        payment.PatientVisit.Doctor.FullName,
                        payment.MedicalService.ServiceName,
                        payment.Amount.ToString("C"),
                        (payment.DoctorShare ?? 0).ToString("C"),
                        payment.CenterShare.ToString("C"),
                        payment.PaymentDate.ToString("HH:mm")
                    );
                }

                ShowSuccess($"تم إنشاء التقرير اليومي لتاريخ {date:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء التقرير اليومي: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task GenerateMonthlyReport(int year, int month, Chart chart, DataGridView grid)
        {
            try
            {
                SetLoadingState(true);

                var monthlySummary = await _reportService.GetMonthlyFinancialSummary(year, month);

                // Update chart
                var series = chart.Series["الإيرادات اليومية"];
                series.Points.Clear();

                foreach (var dailySummary in monthlySummary.DailySummaries)
                {
                    if (dailySummary.TotalRevenue > 0)
                    {
                        series.Points.AddXY(dailySummary.Date.Day, dailySummary.TotalRevenue);
                    }
                }

                // Update grid
                grid.Rows.Clear();
                foreach (var dailySummary in monthlySummary.DailySummaries)
                {
                    if (dailySummary.TotalPayments > 0)
                    {
                        grid.Rows.Add(
                            dailySummary.Date.ToString("yyyy-MM-dd"),
                            dailySummary.TotalMainPayments.ToString("C"),
                            dailySummary.TotalReferralPayments.ToString("C"),
                            dailySummary.TotalRevenue.ToString("C"),
                            dailySummary.TotalDoctorShares.ToString("C"),
                            dailySummary.TotalCenterShares.ToString("C"),
                            dailySummary.TotalPayments
                        );
                    }
                }

                ShowSuccess($"تم إنشاء التقرير الشهري لشهر {month}/{year}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء التقرير الشهري: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task GenerateDoctorPerformanceReport(DateTime startDate, DateTime endDate,
                                                          Chart chart, DataGridView grid)
        {
            try
            {
                SetLoadingState(true);

                var doctorReports = await _reportService.GetDoctorPerformanceReport(startDate, endDate);

                // Update chart
                var series = chart.Series["الإيرادات"];
                series.Points.Clear();

                foreach (var report in doctorReports.Take(10))
                {
                    series.Points.AddXY(report.Doctor.FullName, report.TotalRevenue);
                }

                // Update grid
                grid.Rows.Clear();
                foreach (var report in doctorReports)
                {
                    grid.Rows.Add(
                        report.Doctor.FullName,
                        report.Doctor.Specialty,
                        report.TotalPatients,
                        report.TotalRevenue.ToString("C"),
                        report.TotalDoctorShares.ToString("C"),
                        report.AverageRevenuePerPatient.ToString("C"),
                        report.TotalMainPayments.ToString("C"),
                        report.TotalReferralPayments.ToString("C")
                    );
                }

                ShowSuccess($"تم إنشاء تقرير أداء الأطباء من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء تقرير أداء الأطباء: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task GenerateServiceAnalysisReport(DateTime startDate, DateTime endDate,
                                                        Chart revenueChart, Chart usageChart, DataGridView grid)
        {
            try
            {
                SetLoadingState(true);

                var serviceReports = await _reportService.GetServicePerformanceReport(startDate, endDate);
                var totalRevenue = serviceReports.Sum(s => s.TotalRevenue);

                // Update revenue chart (pie chart)
                var revenueSeries = revenueChart.Series["إيرادات الخدمات"];
                revenueSeries.Points.Clear();

                foreach (var report in serviceReports.Take(8))
                {
                    if (report.TotalRevenue > 0)
                    {
                        revenueSeries.Points.AddXY(report.Service.ServiceName, report.TotalRevenue);
                    }
                }

                // Update usage chart (column chart)
                var usageSeries = usageChart.Series["استخدام الخدمات"];
                usageSeries.Points.Clear();

                foreach (var report in serviceReports.Take(10))
                {
                    if (report.TotalPayments > 0)
                    {
                        usageSeries.Points.AddXY(report.Service.ServiceName, report.TotalPayments);
                    }
                }

                // Update grid
                grid.Rows.Clear();
                foreach (var report in serviceReports)
                {
                    var revenuePercentage = totalRevenue > 0 ? (report.TotalRevenue / totalRevenue) * 100 : 0;

                    grid.Rows.Add(
                        report.Service.ServiceName,
                        report.Service.ServiceType == "Direct" ? "مباشر" : "تحويل",
                        report.TotalPayments,
                        report.TotalRevenue.ToString("C"),
                        report.AverageRevenuePerPayment.ToString("C"),
                        report.TotalDoctorShares.ToString("C"),
                        report.TotalCenterShares.ToString("C"),
                        revenuePercentage.ToString("F1") + "%"
                    );
                }

                ShowSuccess($"تم إنشاء تقرير تحليل الخدمات من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء تقرير تحليل الخدمات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task GenerateTrendsReport(DateTime startDate, DateTime endDate, Chart chart,
                                              Label lblRevenueGrowth, Label lblPaymentGrowth,
                                              Label lblAverageMonthly, Label lblTotalRevenue)
        {
            try
            {
                SetLoadingState(true);

                var trendsReport = await _reportService.GetFinancialTrendsReport(startDate, endDate);

                // Update chart
                var revenueSeries = chart.Series["اتجاه الإيرادات"];
                var doctorSharesSeries = chart.Series["اتجاه حصص الأطباء"];

                revenueSeries.Points.Clear();
                doctorSharesSeries.Points.Clear();

                foreach (var monthlyData in trendsReport.MonthlyData)
                {
                    var monthLabel = $"{monthlyData.Month:D2}/{monthlyData.Year}";
                    revenueSeries.Points.AddXY(monthLabel, monthlyData.TotalRevenue);
                    doctorSharesSeries.Points.AddXY(monthLabel, monthlyData.TotalDoctorShares);
                }

                // Update summary labels
                lblRevenueGrowth.Text = $"معدل نمو الإيرادات: {trendsReport.RevenueGrowthRate:F1}%";
                lblPaymentGrowth.Text = $"معدل نمو المدفوعات: {trendsReport.PaymentGrowthRate:F1}%";
                lblAverageMonthly.Text = $"متوسط الإيرادات الشهرية: {trendsReport.AverageMonthlyRevenue:C}";
                lblTotalRevenue.Text = $"إجمالي الإيرادات: {trendsReport.TotalRevenue:C}";

                // Color code growth rates
                lblRevenueGrowth.ForeColor = trendsReport.RevenueGrowthRate >= 0 ?
                    MaterialDesignHelper.Colors.Success : MaterialDesignHelper.Colors.Error;
                lblPaymentGrowth.ForeColor = trendsReport.PaymentGrowthRate >= 0 ?
                    MaterialDesignHelper.Colors.Success : MaterialDesignHelper.Colors.Error;

                ShowSuccess($"تم إنشاء تقرير الاتجاهات المالية من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء تقرير الاتجاهات المالية: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        // Export Methods
        private async Task ExportDailyReport(DateTime date)
        {
            try
            {
                var summary = await _reportService.GetDailyFinancialSummary(date);
                var fileName = $"تقرير_يومي_{date:yyyy-MM-dd}.csv";
                var filePath = _exportService.ShowSaveFileDialog(fileName);

                if (!string.IsNullOrEmpty(filePath))
                {
                    SetLoadingState(true);
                    var success = await _exportService.ExportDailyReportToCSV(summary, filePath);

                    if (success)
                    {
                        ShowSuccess($"تم تصدير التقرير اليومي بنجاح إلى: {filePath}");
                    }
                    else
                    {
                        ShowError("فشل في تصدير التقرير");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تصدير التقرير اليومي: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task ExportMonthlyReport(int year, int month)
        {
            try
            {
                var summary = await _reportService.GetMonthlyFinancialSummary(year, month);
                var fileName = $"تقرير_شهري_{year}-{month:D2}.csv";
                var filePath = _exportService.ShowSaveFileDialog(fileName);

                if (!string.IsNullOrEmpty(filePath))
                {
                    SetLoadingState(true);
                    var success = await _exportService.ExportMonthlyReportToCSV(summary, filePath);

                    if (success)
                    {
                        ShowSuccess($"تم تصدير التقرير الشهري بنجاح إلى: {filePath}");
                    }
                    else
                    {
                        ShowError("فشل في تصدير التقرير");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تصدير التقرير الشهري: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task ExportDoctorPerformanceReport(DateTime startDate, DateTime endDate)
        {
            try
            {
                var reports = await _reportService.GetDoctorPerformanceReport(startDate, endDate);
                var fileName = $"تقرير_أداء_الأطباء_{startDate:yyyy-MM-dd}_إلى_{endDate:yyyy-MM-dd}.csv";
                var filePath = _exportService.ShowSaveFileDialog(fileName);

                if (!string.IsNullOrEmpty(filePath))
                {
                    SetLoadingState(true);
                    var success = await _exportService.ExportDoctorPerformanceToCSV(reports, startDate, endDate, filePath);

                    if (success)
                    {
                        ShowSuccess($"تم تصدير تقرير أداء الأطباء بنجاح إلى: {filePath}");
                    }
                    else
                    {
                        ShowError("فشل في تصدير التقرير");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تصدير تقرير أداء الأطباء: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task ExportServiceAnalysisReport(DateTime startDate, DateTime endDate)
        {
            try
            {
                var reports = await _reportService.GetServicePerformanceReport(startDate, endDate);
                var fileName = $"تقرير_تحليل_الخدمات_{startDate:yyyy-MM-dd}_إلى_{endDate:yyyy-MM-dd}.csv";
                var filePath = _exportService.ShowSaveFileDialog(fileName);

                if (!string.IsNullOrEmpty(filePath))
                {
                    SetLoadingState(true);
                    var success = await _exportService.ExportServiceAnalysisToCSV(reports, startDate, endDate, filePath);

                    if (success)
                    {
                        ShowSuccess($"تم تصدير تقرير تحليل الخدمات بنجاح إلى: {filePath}");
                    }
                    else
                    {
                        ShowError("فشل في تصدير التقرير");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تصدير تقرير تحليل الخدمات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async Task ExportTrendsReport(DateTime startDate, DateTime endDate)
        {
            try
            {
                var report = await _reportService.GetFinancialTrendsReport(startDate, endDate);
                var fileName = $"تقرير_الاتجاهات_المالية_{startDate:yyyy-MM-dd}_إلى_{endDate:yyyy-MM-dd}.csv";
                var filePath = _exportService.ShowSaveFileDialog(fileName);

                if (!string.IsNullOrEmpty(filePath))
                {
                    SetLoadingState(true);
                    var success = await _exportService.ExportFinancialTrendsToCSV(report, filePath);

                    if (success)
                    {
                        ShowSuccess($"تم تصدير تقرير الاتجاهات المالية بنجاح إلى: {filePath}");
                    }
                    else
                    {
                        ShowError("فشل في تصدير التقرير");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تصدير تقرير الاتجاهات المالية: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }
    }
}
